<template>
  <div class="manager_leaderboard_card_container">
    <!-- 排名 -->
    <div class="ranking">
        <img v-if="props.rank <= 3" :src="rankIcons[props.rank]" :alt="`第${props.rank}名`" class="ranking_img" />
        <span v-else class="rank-number">{{ props.rank }}.</span>
    </div>
    <!-- 店长 -->
    <div class="store_manager">
        <img :src="rankingInfo?.img || defaultAvatar" alt="" class="store-avatar" />
      <div class="store_manager_name">{{ rankingInfo?.name || '店长' }}</div>
    </div>
    <!-- 销售额 -->
    <div class="store_volume">
        {{ rankingInfo.totalAmount ? `${(rankingInfo.totalAmount / 100).toFixed(2)}` : '0.00' }}
        <span class="store_volume_unit">元</span>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { toRefs } from "vue";
/** 相关静态资源 */
import ranking_1 from "@/assets/storeImage/storeHome/ranking_1.png";
import ranking_2 from "@/assets/storeImage/storeHome/ranking_2.png";
import ranking_3 from "@/assets/storeImage/storeHome/ranking_3.png";
import defaultAvatar from "@/assets/image/system/account/defaultAvatar.jpg";

defineOptions({ name: "ManagerLeaderboardCard" });

/** props */
const props = defineProps<{
    rank?: number;
    rankingInfo?: {
        rank: number;
        img: string;
        name: string;
        totalAmount: number;
    };
}>();

const { rank, rankingInfo } = toRefs(props);

// 排名图标映射
const rankIcons: Record<number, string> = {
  1: ranking_1,
  2: ranking_2,
  3: ranking_3
};
</script>

<style lang="less" scoped>
.manager_leaderboard_card_container {
    width: 100%;
    height: 62px;
    background-color: #fff;
    box-sizing: border-box;
    display: flex;
    align-items: center;

    .ranking {
        flex: .6;
        font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
        font-weight: 400;
        font-size: 20px;
        color: #333333;
        text-align: left;
        font-style: normal;
        text-transform: none;
        .ranking_img {
            width: 32px;
            height: 32px;
        }

        .rank-number {
          font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
          font-weight: 400;
          font-size: 20px;
          color: #333333;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
    }

    .store_manager {
        flex: 2;
        display: flex;
        align-items: center;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 16px;
        color: #333333;
        text-align: left;
        font-style: normal;
        text-transform: none;
        .store-avatar {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          object-fit: cover;
          flex-shrink: 0;
          margin-right: 8px;
        }
        .store_manager_name {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 500;
            font-size: 16px;
            color: #333333;
            text-align: left;
            font-style: normal;
            text-transform: none;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }

    .store_volume {
        flex: 1;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 18px;
        color: #333333;
        text-align: left;
        font-style: normal;
        text-transform: none;
        .store_volume_unit {
            font-size: 14px;
        }
    }
}
</style>
