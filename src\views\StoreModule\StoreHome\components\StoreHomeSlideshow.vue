<template>
  <VanSwipe v-if="swiperList.length" :autoplay="3000" indicator-color="white" class="store-home-slideshow">
    <VanSwipeItem v-for="item in swiperList" :key="item.id">
      <div class="store-home-slideshow-item" @click="handleSwiperClick(item)">
        <img :src="item.imgPath" alt="" />
      </div>
    </VanSwipeItem>
  </VanSwipe>
</template>

<script lang="ts" setup>
import { onMounted } from "vue";
import useGetHomeSwiper from "../hooks/useGetHomeSwiper";

defineOptions({ name: 'StoreHomeSlideshow' });

const { swiperList, getHomeSwiper, handleSwiperClick } = useGetHomeSwiper();

/** 组件挂载 */
onMounted(() => {
  getHomeSwiper();
});
</script>

<style lang="less" scoped>
.store-home-slideshow {
  width: 100%;
  height: 160px;
  .store-home-slideshow-item {
    width: 100%;
    height: 100%;
    img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
