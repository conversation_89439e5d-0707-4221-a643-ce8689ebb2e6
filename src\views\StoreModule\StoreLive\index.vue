<template>
  <div class="live-sensitive-words">
    <!-- 用户信息栏 -->
    <div class="user">
      <div class="userInfo">
        <van-image round width="40" height="40" fit="contain" :src="userStore.storeUserInfo.img || defaultAvatar" />
        <span>{{ userStore.storeUserInfo.nickname || '-' }}</span>
      </div>
      <div class="handle">
        <!-- 个人中心入口 -->
        <div class="center" @click="handelPersonalCenter">个人中心</div>
        <van-image
          :src="shareIcon"
          fit="cover"
          width="16"
          height="16"
          :lazy-load="true"
          @click="showSharePopup = true"
        />
      </div>
    </div>

    <!-- 福利组件 - 只在有直播链接时显示 -->
    <WelfareWidget v-if="liveInfo.hsLiveLink" :liveInfo="liveInfo" />

    <!-- 主要内容区域 -->
    <div class="warpper">
      <!-- 直播中：显示iframe -->
      <iframe
        v-if="liveInfo.status == STREAM_STATUS.PROGRESS"
        allowfullscreen="true"
        allow="fullscreen;screen-wake-lock;camera;microphone"
        :src="liveInfo.hsLiveLink ? liveInfo.hsLiveLink + '?platform=mobile' : ''"
      ></iframe>

      <!-- 未绑定门店：显示绑定提示 -->
      <div class="unbound" v-else-if="liveInfo.status == STREAM_STATUS.UNBOUND">
        <div>
          <div>请先扫【会员邀请码】绑定门店，再观看直播</div>
          <div class="marginTopStyle">
            <van-button type="default" @click="handelScan">扫 一 扫</van-button>
          </div>
          <div class="backHome" @click="backHome">返回首页</div>
        </div>
      </div>

      <!-- 其他状态：直播结束、失效等 -->
      <div class="unbound" v-else>
        <div>
          <!-- 状态图片 -->
          <div class="imgStyle">
            <van-image
              fit="contain"
              :src="liveInfo.status == STREAM_STATUS.EFFICACY ? liveEfficacyImg : liveEmptyImg"
            />
          </div>
          <!-- 状态文字提示 -->
          <div class="marginTopStyle">
            <div v-if="liveInfo.status == STREAM_STATUS.END">直播已结束</div>
            <div v-else-if="liveInfo.status == STREAM_STATUS.EFFICACY">链接已失效</div>
            <div v-else-if="liveInfo.status == STREAM_STATUS.DELEAT">课程已被删除</div>
            <div v-else-if="liveInfo.status == STREAM_STATUS.NOT_AUTH_CUSTOMER">暂无权限观看直播!</div>
          </div>
          <!-- 返回首页按钮 -->
          <div class="marginTopStyle">
            <van-button type="primary" @click="backHome">返回首页</van-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 分享弹窗 -->
    <SharePopup v-model:show="showSharePopup" :liveRoomId="liveInfo.liveRoomId" :liveStatus="liveInfo.status" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useUserStore } from '@/stores/modules/user';
import { parseUrlParams } from '@/utils/http/urlUtils';
import { useMessages } from "@/hooks/useMessage";
import { useRouter, useRoute } from "vue-router"
import { RoutesName } from "@/enums/routes";
import { routesMap } from "@/router/maps";
import { wxSdkInit } from "@/utils/wxSDKUtils";
import { useSystemStoreWithoutSetup } from "@/stores/modules/system"
import { isIOSEnv } from "@/utils/isUtils"
import { CacheConfig } from "@/utils/cache/config"
import { Cache_Key } from "@/enums/cache";
import { createCacheStorage } from "@/utils/cache/storageCache"
import { isInFrame } from '@/utils/envUtils';
import { MessageEventEnum, useWindowMessage } from '@/hooks/useWindowMessage';
import { getJSSDKConfig } from "@/services/storeApi";
import { getHsLiveLink, type HsLiveLinkResponse } from "@/services/storeApi";
/** 相关资源 */
import defaultAvatar from "@/assets/image/system/account/defaultAvatar.jpg";
import liveEmptyImg from "@/assets/store/liveEmptyImg.png";
import liveEfficacyImg from "@/assets/store/efficacy.png";
import shareIcon from "@/assets/storeImage/live/share.png";
/** 相关组件 */
import WelfareWidget from '@/views/StoreModule/StoreLive/components/WelfareWidget/index.vue';
import SharePopup from '@/views/StoreModule/StoreLive/components/SharePopup/index.vue';
import {storeRefreshUserInfo} from "@/views/StoreModule/utils/accountUtils";

const { createMessageError } = useMessages();
const { sendMessageToWindows } = useWindowMessage();
const router = useRouter();
const route = useRoute();
const userStore = useUserStore();
const systemStore = useSystemStoreWithoutSetup();

// 直播状态常量定义
const enum STREAM_STATUS {
    PROGRESS = 0,          // 直播中
    UNBOUND = 1,           // 已注册未绑定门店
    DELEAT = 2,            // 直播课已删除
    END = 3,               // 直播已结束
    EFFICACY = 4,          // 链接已失效
    NOT_AUTH_CUSTOMER = 6, // 暂无权限观看直播!
};

// 响应式数据
const liveInfo = ref<HsLiveLinkResponse>({
    status: 0,
    hsLiveLink: '',
    /* 直播间id */
    liveRoomId: '',
    /** 是否启用广告牌 */
    billboardEnable: 0,
    /** 广告牌图片 */
    billboardImageUrl: '',
    /** 广告牌跳转链接 */
    billboardLinkUrl: '',
    /** 是否新归属 */
    newBelong:0
});

const showSharePopup = ref(false); // 分享弹窗显示状态
const initJSSDKFlagRef = ref(false); // JSSDK初始化标志

// 计算属性：是否启用广告牌
const billboardEnable = computed(() => {
    return liveInfo.value.billboardEnable == 1;
});

const orderCodeCache = createCacheStorage(CacheConfig.StoreOrderCache);

/**
 * 初始化直播信息
 */
async function initLiveInfo() {
    // 从URL参数中获取state参数
    const params = parseUrlParams(location.search);
    try {
        const resp = await getHsLiveLink(params.state as string, params.userId || "" as string);
        liveInfo.value = resp;
        if (resp.newBelong == 1) {
            userStore.setToken(resp.token)
            userStore.setStoreToken(resp.token);
            storeRefreshUserInfo();
        }
        // 如果是未绑定状态，初始化JSSDK用于扫码
        if (liveInfo.value.status === STREAM_STATUS.UNBOUND) {
            initJSSDKFlagRef.value = await initJSSDK();
        }
    } catch (error) {
        createMessageError(`请求失败: ${error}`);
    }
}

/**
 * 设置本地存储变化监听器
 * 用于监听订单码的变化，当有新的订单时跳转到收银台
 */
function setupStorageListener() {
    window.addEventListener('storage', function (e: StorageEvent) {
        if (e.key === Cache_Key.StoreOrderCode) {
            handleOrderCodeChange();
        }
    });
}

/**
 * 处理订单码变化
 * 当检测到新的订单码时，跳转到收银台页面
 */
function handleOrderCodeChange() {
    const orderCode = orderCodeCache.get() as string;
    if (!orderCode) return;

    // 判断是否在iframe中运行
    if (isInFrame()) {
        const url = `${location.origin}/st/cashier?orderCode=${orderCode}&state=${route.query.state}&isLive=1`;
        if (isIOSEnv()) {
            // iOS环境通过消息传递URL
            sendMessageToWindows(MessageEventEnum.sendUrlToWindow, url);
        } else {
            // 其他环境直接打开新窗口
            window.open(url);
        }
    } else {
        // 非iframe环境直接路由跳转
        router.replace({
            name: RoutesName.StoreCashier,
            query: {
                ...route.query,
                orderCode,
                isLive: 1
            }
        });
    }
}

/**
 * 初始化微信JSSDK
 * @returns 初始化是否成功
 */
async function initJSSDK(): Promise<boolean> {
    // iOS环境使用入口URL，其他环境使用当前页面URL
    const _url = isIOSEnv() ? systemStore.entryUrl : `${window.location.origin}${route.fullPath}`;
    try {
        // 获取JSSDK配置
        const { signature, nonceStr, timestamp } = await getJSSDKConfig(_url);
        const stateCache = createCacheStorage(CacheConfig.StoreToken);
        const _stateInfo = stateCache.get();

        // JSSDK配置参数
        const JSSDK_Config_params = {
            debug: false,
            appId: _stateInfo.wxappId,
            timestamp,
            nonceStr,
            signature,
            jsApiList: ['scanQRCode'], // 需要使用的JS接口列表
        };

        // 初始化JSSDK
        await wxSdkInit(JSSDK_Config_params);
        return true;
    } catch (error) {
        console.log('Init WeChat JSSDK error:', error);
        return false;
    }
}

/**
 * 处理扫码操作
 * 调用微信扫一扫功能
 */
function handelScan() {
    if (initJSSDKFlagRef.value) {
        window.wx.scanQRCode({
            needResult: 0, // 不需要返回结果
            scanType: ["qrCode", "barCode"], // 扫码类型
            success: function (res) {
                // 扫码成功处理
                console.log('Scan result:', res.resultStr);
            }
        });
    } else {
        createMessageError('JSSDK Not Ready');
    }
}

/**
 * 跳转到个人中心
 */
function handelPersonalCenter() {
    router.push(routesMap[RoutesName.StoreMine]);
}

/**
 * 返回首页
 */
function backHome() {
    router.push(routesMap[RoutesName.StoreHome]);
}

// 组件挂载时初始化
onMounted(async () => {
    await initLiveInfo();    // 初始化直播信息
    setupStorageListener();  // 设置监听器
});
</script>

<style lang="less" scoped>
.live-sensitive-words {
    width: 100%;
    height: 100vh;
    background-color: white;

    .user {
        width: 95%;
        height: 40px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: auto;
        font-size: 15px;
        padding-top: 5px;
    }

    .userInfo {
        display: flex;
        align-items: center;

        span {
            margin-left: 10px;
        }
    }

    .handle {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .center {
        font-size: 14px;
    }

    .warpper {
        width: 100%;
        height: calc(100% - 53px);
        margin-top: 8px;

        iframe {
            width: 100%;
            height: 100%;
        }

        .unbound {
            width: 100%;
            height: 100%;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;

            .backHome {
                color: #1677FF;
                margin-top: 30px;
            }

            .marginTopStyle {
                margin-top: 20px;
            }

            .imgStyle {
                width: 240px;
                height: 150px;

                .van-image {
                    width: 100%;
                    height: 100%;
                }
            }
        }
    }
}
</style>
