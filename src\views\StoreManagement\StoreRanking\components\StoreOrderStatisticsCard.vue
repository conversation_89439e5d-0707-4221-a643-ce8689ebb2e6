<template>
  <div class="rank-card" @click="handleToStoreSalesStatistics(item)">
    <!-- 排名区域 -->
    <div class="rank-card__order">
      <img v-if="props.rank <= 3" :src="rankIcons[props.rank]" :alt="`第${props.rank}名`" class="rank-icon" />
      <span v-else class="rank-number">{{ props.rank }}.</span>
    </div>

    <!-- 店铺信息区域 -->
    <div class="rank-card__info">
      <img :src="item?.img || defaultAvatar" alt="" class="store-avatar" />
      <span class="store-name">{{ item.name }}</span>
    </div>

    <!-- 数据区域 -->
    <div class="rank-card__data">
      {{ displayData }}
      <span class="rank-card__unit">{{ item.type == OrderSortTypeEnum.AMOUNT ? '元' : '单'}}</span>
      <img v-if="item.type == OrderSortTypeEnum.AMOUNT" :src="rightIcon" alt="" class="rank-card__icon" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { OrderSortTypeEnum, RankTimeRangeEnum } from "@/views/StoreModule/enums";
import { useRouterUtils } from "@/views/StoreModule/hooks";
import { RoutesName } from "@/enums/routes";
import { KeepAliveRouteNameEnum } from "@/views/StoreModule/enums";
import { useKeepAliveRoute } from '@/hooks/useKeepAliveRoute';
/** 资源 */
import ranking_1 from "@/assets/storeImage/storeHome/ranking_1.png";
import ranking_2 from "@/assets/storeImage/storeHome/ranking_2.png";
import ranking_3 from "@/assets/storeImage/storeHome/ranking_3.png";
import rightIcon from "@/assets/storeImage/storeMine/rightIcon.png";
import defaultAvatar from "@/assets/image/system/account/defaultAvatar.jpg";

defineOptions({ name: 'StoreRankCard' });

interface RankItem {
  img?: string;
  name?: string;
  totalAmount?: number;
  orderNum?: number;
  type?: number;
  rank?: number;
  memberId?: number;
  staffId?: number;
  memberShortId?: string;
  staffShortId?: string;  
}

const props = defineProps<{
  rank?: number;
  item: RankItem;
  dataRange: RankTimeRangeEnum;
}>();

const { pushKeepAliveRoute, deleteKeepAliveRouteByName, scrollEventHandler, restoreScrollPositionByDom, _findIndex } = useKeepAliveRoute();

// 排名图标映射
const rankIcons: Record<number, string> = {
  1: ranking_1,
  2: ranking_2,
  3: ranking_3
};

const { routerPushByRouteName } = useRouterUtils();

// 计算显示数据
const displayData = computed(() => {
  const { item } = props;
  if (item.type === 1) {
    return item.totalAmount ? `${(item.totalAmount / 100).toFixed(2)}` : '0.00';
  }
  return item.orderNum ? `${item.orderNum}` : '0';
});

function handleToStoreSalesStatistics(item: RankItem) {
  pushKeepAliveRoute(KeepAliveRouteNameEnum.MEMBER_RANKING);
  routerPushByRouteName(RoutesName.StoreSalesStatistics, {
    memberId: item.memberShortId,
    dataRange: props.dataRange
  });
}
</script>

<style lang="less" scoped>
.rank-card {
  display: flex;
  align-items: center;
  height: 64px;
  width: 100%;
  background: #fff;
  border-radius: 8px;
  margin-bottom: 8px;
  box-sizing: border-box;
  padding: 12px;

  &__order {
    flex: .8;
    display: flex;
    align-items: center;

    .rank-icon {
      width: 32px;
      height: 32px;
    }

    .rank-number {
      font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
      font-weight: 400;
      font-size: 20px;
      color: #333333;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }

  &__info {
    flex: 2;
    display: flex;
    align-items: center;

    .store-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      object-fit: cover;
      flex-shrink: 0;
      margin-right: 8px;
    }

    .store-name {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 500;
      font-size: 16px;
      color: #333333;
      text-align: left;
      font-style: normal;
      text-transform: none;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  &__data {
    flex: 1;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 18px;
    color: #333333;
    font-style: normal;
    text-transform: none;
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
  }

  &__unit {
    font-size: 15px;
  }

  &__icon {
    width: 18px;
    height: 18px;
    margin-left: 2px;
  }
}
</style>
