<template>
    <JLoadingWrapper :show="isLoading">
        <div class="confirm-order">
            <div v-if="isSelfPickup" class="card-box">
                <div class="card-header">取货地点</div>
                <template v-if="tempVal.storeEntity?.id">
                    <div @click="jumpNearbyPickupPoint" class="address-content">
                        <img class="content-img" :src="tempVal.storeEntity.storeAvatar || storeSrc" alt="" srcset="" />
                        <div class="content-info">
                            <div class="info-title">{{ tempVal.storeEntity.storeName || '--' }}</div>
                            <div class="info-footer">
                                <div class="footer-info">
                                    <img :src="addressIcon" alt="" srcset="" />
                                    <div class="desc">
                                        {{
                                            `${tempVal.storeEntity.province || '--'}${tempVal.storeEntity.city || '--'}${tempVal.storeEntity.area ||
                                            '--'}${tempVal.storeEntity.addressDetail || '--'}`
                                        }}
                                    </div>
                                </div>
                                <div class="footer-info">
                                    <img :src="phoneIcon" alt="" srcset="" />
                                    <div class="desc">
                                        <div class="desc-left">{{ tempVal.storeEntity.contactName || '--' }}</div>
                                        <div class="desc-right">{{ tempVal.storeEntity.contactPhone || '--' }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
                <template v-else>
                    <div class="no-options-available" @click="jumpNearbyPickupPoint">
                        <img class="address_icon_blue" src="@/assets/storeImage/product/confirmOrder/address_icon_blue.png" alt="">
                        <div>选择附近自提点</div>
                        <img class="right_icon" src="@/assets/storeImage/product/confirmOrder/right_icon.png" alt="">
                    </div>
                </template>
                <div class="card-footer">
                    <span>请在通知取货后，凭</span>
                    <span style="color: #666666;">提货码</span>
                    <span>到上方门店取货，如有售后问题，请联系门店。</span>
                </div>
            </div>
            <div v-if="isNeedCustomerInfo && !isExpressDelivery" class="card-box confirm-info">
                 <div class="card-header">信息确认</div>
                    <van-cell-group  inset>
                        <van-field required v-model="defaultAddress.name" label="姓名" placeholder="请输入姓名" />
                        <van-field required v-model="defaultAddress.mobile" type="tel" label="手机号" placeholder="请输入手机号" />
                    </van-cell-group>

            </div>
            <!-- 收货地址 -->
            <StoreDeliveryAddress v-if="isExpressDelivery" :addressInfo="defaultAddress" @click="handleAddAddress" />
            <div class="card-box">
                <div class="card-header">商品</div>
                <div class="goods-content" v-for="item in tempVal.cartItemDTOList" :key="item.productId">
                    <img :src="item.path" alt="" srcset="" />
                    <div class="goods-info">
                        <GoodsTitle :title="productStr(item)" class="info-title" :is-auto-computed-title="false">
                        </GoodsTitle>
                        <div class="info-desc">
                            {{ item.specName }}
                        </div>
                        <div class="info-price">
                            <PriceContent :price="getPriceOractivity(item)" v-if="props.type == StoreGoodsEnum.Goods"
                                price-font-size="24px" />
                            <WelfareContent :sku-info="item" v-if="props.type == StoreGoodsEnum.WelfareTicket"
                                :isShowSuffux="false"></WelfareContent>
                            <div class="price-num">x{{ item.count }}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-box"
                v-if="props.type == StoreGoodsEnum.Goods || props.type == StoreGoodsEnum.IntegralGoods">
                <div class="desc-content">
                    <div class="desc-info">
                        <div class="info">
                            <div class="info-left">商品金额</div>
                            <PriceContent :price="tempVal.goodsAmount" price-font-size="16px" />
                        </div>
                        <div class="info" v-if="type == StoreGoodsEnum.IntegralGoods">
                            <div class="info-left">积分支付</div>
                            <IntegralContent :points="tempVal.totalPoints" numFontSize="16px" pricePreFontSize="10px" />
                        </div>
                        <div class="info">
                            <div class="info-left">邮费</div>
                            <PriceContent :price="tempVal.shippingFee" price-font-size="16px" />
                        </div>
                        <div class="info" v-if="props.type == StoreGoodsEnum.Goods">
                            <div class="info-left">现金券</div>
                            <div class="info-right" @click="jumpCupon">
                                <PriceContent :price="tempVal.cashCouponAmt" v-if="tempVal.maxCashCouponAmt>0 && tempVal.cashCouponAmt > 0" prefix="-" :is-prefix="true"
                                    price-font-size="16px" style="margin-right: 4px;" />
                                <span style="color: #999;font-size:14px;" v-else-if="tempVal.maxCashCouponAmt>0 && tempVal.cashCouponAmt <= 0 && isUseCoupon">有可用现金抵扣券</span>
                                <span style="color: #999;font-size:14px;" v-else>暂无可用现金抵扣券</span>
                                <van-icon name="arrow" size="14px" />
                            </div>
                        </div>
                    </div>
                    <div class="desc-sum">
                        <div class="sum-name">合计：</div>
                        <div class="coupon" v-if="props.type == StoreGoodsEnum.Goods && tempVal.maxCashCouponAmt > 0 && tempVal.cashCouponAmt > 0">
                            已优惠￥{{Number(tempVal.cashCouponAmt / 100).toFixed(2)}}&nbsp;
                        </div>
                        <IntegralContent :points="tempVal.totalPoints" :price="tempVal.money" price-font-size="24px" />
                    </div>
                </div>
                <div class="returnPoints" v-if="tempVal.returnPoints > 0">
                    <img class="coins" :src="coinsIcon" />
                    <span>
                        订单完成后可获得
                        <span class="number">{{ tempVal.returnPoints }}</span>
                        积分
                    </span>
                </div>
            </div>
            <!-- 提交订单 -->
            <div class="footer">
                <div class="footer-content">
                    <!-- 商品详情 -->
                    <div class="footer-left"
                        v-if="props.type == StoreGoodsEnum.Goods || props.type == StoreGoodsEnum.IntegralGoods">
                        <img :src="cartLogo" />
                        <div class="left-content">
                            <div class="content">
                                <div class="footer-text">需付款</div>
                                <div class="footer-price">
                                    <PriceContent :price="tempVal.money" price-font-size="24px" />
                                </div>
                            </div>
                            <div class="coupon" v-if="props.type == StoreGoodsEnum.Goods && tempVal.maxCashCouponAmt > 0 && tempVal.cashCouponAmt > 0">
                                已优惠￥{{ Number(tempVal.cashCouponAmt / 100).toFixed(2) }}
                            </div>
                        </div>
                    </div>
                    <!-- 商品详情 -->
                    <div class="footer-left" v-if="props.type == StoreGoodsEnum.WelfareTicket">
                        <img :src="cartLogo" />
                        <div class="left-content">
                            <div class="content">
                                <div class="footer-text">需核销：</div>
                                <div class="footer-price">
                                    <div class="welfare">
                                        <div class="price">{{ tempVal.totalCoupons }}张</div>
                                        <div class="desc">{{ goodsDataInfo.couponName || '' }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="footer-right" @click="payFn">提交订单</div>
                </div>
            </div>
        </div>
    </JLoadingWrapper>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, computed, onActivated, onUnmounted } from "vue";
import { showToast } from "vant";
import PriceContent from "../components/PriceContent.vue";
import IntegralContent from "../components/IntegralContent.vue";
import WelfareContent from "../components/WelfareContent.vue";
import GoodsTitle from "../components/GoodsTitle.vue";
import addressIcon from "@/assets/storeImage/product/confirmOrder/addressIcon.png";
import phoneIcon from "@/assets/storeImage/product/confirmOrder/phoneIcon.png";
import cartLogo from "@/assets/storeImage/product/confirmOrder/cartLogo.png";
import coinsIcon from "@/assets/storeImage/product/confirmOrder/coins.png";
import { StoreGoodsEnum } from "@/enums/storeGoods";
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";
import { createOrder, getRequestNo, getCouponRequestNo, couponCreateOrder, pointConfirmOrder, getPickUpStore, getNeedCustomerInfo } from "@/services/storeApi";
import { useMessages } from "@/hooks/useMessage";
import { isInFrame } from "@/utils/envUtils"
import { isObject } from "@/utils/isUtils";
import { useRouter, useRoute } from "vue-router";
import { GoodsTypeEnum, PayTypeEnum } from "@/enums/storeGoods";
import { createCacheStorage } from "@/utils/cache/storageCache";
import { CacheConfig } from "@/utils/cache/config";
import { RoutesName } from "@/enums/routes";
import { useCoupon } from "./hooks/useCoupon";
import { KeepAliveRouteNameEnum, ProductPickupModeEnum, StoreAddressRouteTypeEnum } from "@/views/StoreModule/enums";
import { useRouterUtils } from "@/views/StoreModule/hooks";
import { useKeepAliveRoute } from '@/hooks/useKeepAliveRoute';
import { useUserAddress } from "../hooks";
/** 静态资源 */
import storeSrc from "@/assets/storeImage/storeMine/store.png";
/** 相关组件 */
import StoreDeliveryAddress from "./components/StoreDeliveryAddress.vue";

defineOptions({
    name: KeepAliveRouteNameEnum.CONFIRM_ORDER,
});

/** props */
const props = withDefaults(defineProps<{
    type: StoreGoodsEnum,
    orderInfo: string,
    liveId: string
}>(), {
    type: StoreGoodsEnum.Goods,
    orderInfo: "",
    liveId: ''
});

const router = useRouter();
const route = useRoute();
const message = useMessages();
const { routerPushByRouteName } = useRouterUtils();
const { pushKeepAliveRoute, deleteKeepAliveRouteByName } = useKeepAliveRoute();
const { currentAddressInfo, defaultAddress, isDefaultAddress } = useUserAddress();
const { isUseCoupon,getUseCouponList } = useCoupon();

const tempVal = reactive({
    cartItemDTOList: [],
    //最佳组合
    selectedCashCouponIdList: [],
    selectIds:[],
    money: 0,
    goodsAmount: 0,
    totalPoints: 0,
    shippingFee: 0,
    returnPoints: 0,
    requestNo: null,
    totalCoupons: 0,
    maxCashCouponAmt: 0,
    cashCouponAmt: 0,
    storeEntity:{} as any,
    info: {
        name: "",
        phone: "",
    }
})
const isLoading = ref<boolean>(false);
const reuseNum = ref<number>(0)
const productStr = (item) => {
    let ansName = ''
    if (!item.productFrontName) {
        ansName = item.productName
    } else {
        if (item.type == GoodsTypeEnum.OTC_DRUG) {
            ansName = `[${item.productFrontName}]${item.productName}${item.specName}`
        } else {
            ansName = item.productFrontName
        }
    }
    return ansName
}
const goodsDataInfo = computed(() => {
    return tempVal.cartItemDTOList.length ? tempVal.cartItemDTOList[0] : {}
});

/** 是否自提商品 */
const isSelfPickup = computed(() => {
    return goodsDataInfo.value?.deliveryType == ProductPickupModeEnum.STORE_PICKUP;
});

/** 是否快递到家商品 */
const isExpressDelivery = computed(() => {
    return goodsDataInfo.value?.deliveryType == ProductPickupModeEnum.HOME_DELIVERY;
});
const isPickUpChange = ref(false);
const isNeedCustomerInfo = ref(false);
/** 计算价格或活动价格 */
const getPriceOractivity = (item) => {
    return item.priceType == 0 ? item.price : item.activityPrice;
}

/** 添加地址 */
function handleAddAddress() {
    pushKeepAliveRoute(KeepAliveRouteNameEnum.CONFIRM_ORDER);
    routerPushByRouteName(RoutesName.StoreAddress, { routeType: StoreAddressRouteTypeEnum.ORDER_ADDRESS });
}

/** 确认订单 */
async function payFn() {
    isLoading.value = true;
    reuseNum.value = 0;

    try {
        if (!isDefaultAddress.value && isExpressDelivery.value) {
            showToast('请设置收货地址');
            return;
        }
        if (isSelfPickup.value && tempVal.storeEntity?.storeId) {
            showToast('请先选择自提点');
            return;
        }
        if (isNeedCustomerInfo.value && !defaultAddress.name && !defaultAddress.mobile) {
            showToast('请先填写信息确认');
            return;
        }
        if (isNeedCustomerInfo.value && defaultAddress.mobile && !/^1[3-9]\d{9}$/.test(defaultAddress.mobile)) {
            showToast('请输入正确的手机号');
            return;
        }
        if(isNeedCustomerInfo.value && !isExpressDelivery.value) {
            defaultAddress.type = 4
        }
        const params: any = {
            transno: tempVal.requestNo,
            data: {
                cartItemVOList: tempVal.cartItemDTOList,
                customerAddressVO: isExpressDelivery.value || isNeedCustomerInfo.value ? defaultAddress : undefined, // 地址信息
                payType: PayTypeEnum.OnlinePay,
                money: tempVal.money,
                storeId: isSelfPickup.value && isPickUpChange.value ? tempVal.storeEntity?.id : undefined,
                ...(props.liveId && { liveId: props.liveId })
            }
        };

        if(props.type == StoreGoodsEnum.Goods) {
            params.data.selectedCashCouponIdList = tempVal.selectIds;
        }
        const apiMap = {
            [StoreGoodsEnum.Goods]: createOrder,
            [StoreGoodsEnum.IntegralGoods]: pointConfirmOrder,
            [StoreGoodsEnum.WelfareTicket]: couponCreateOrder
        };

        const api = apiMap[props.type];
        if (!api) throw new Error("Invalid store goods type");

        const res = await api(params);

        // 福利券返回上一个页面
        if (props.type == StoreGoodsEnum.WelfareTicket) {
            message.createMessageSuccess({
                content: "兑换成功！",
                duration: 1500,
                onAfterLeave: () => router.back()
            });
            return;
        }

        const orderCode = isObject(res) ? res.code : res;

        if (isInFrame()) {
            createCacheStorage(CacheConfig.StoreOrderCache).set(orderCode);
            // window.open(`${location.origin}/st/cashier?orderCode=${orderCode}&state=${route.query.state}`)
            return;
        }

        router.replace({
            name: RoutesName.StoreCashier,
            query: { ...route.query, orderCode }
        });
    } catch (error) {
        isTwo409(error);
    } finally {
        isLoading.value = false;
    }
};

/** 重复两次才报错 */
const isTwo409 = (err) => {
    // 重复两次就提示错误
    if (err?.includes("无幂等性") || err?.includes("已重复提交")) {
        if (reuseNum.value < 1) {
            reuseNum.value++
            GetRequestNoFn()
        } else {
            reuseNum.value = 0
            message.createMessageError(err)
        }
    } else {
        message.createMessageError(`获取预支付信息失败:${err}`)
    }
}

/** 重新获取请求单号 */
const GetRequestNoFn = async () => {
    try {
        let api;
        if (props.type == StoreGoodsEnum.Goods || props.type == StoreGoodsEnum.IntegralGoods) {
            api = getRequestNo
        }
        if (props.type == StoreGoodsEnum.WelfareTicket) {
            api = getCouponRequestNo
        }
        const res = await api()
        tempVal.requestNo = res
        payFn()
    } catch (error) {
        console.log(error);
    }
}
/**现金券跳转 */
const jumpCupon = () => {
    router.replace({
        name: RoutesName.StoreCashCoupon,
        query: {
            orderInfo: encodeURIComponent(JSON.stringify(tempVal))
        }
    })
}
const jumpNearbyPickupPoint = () => {
    if(!isPickUpChange.value) return
    router.replace({
        name: RoutesName.StorePickUpLocation,
        query: {
            orderInfo: encodeURIComponent(JSON.stringify(tempVal)),
            type: props.type
        }
    })
}
const getIsNeedCustomerInfo = async () => {
    const res = await getNeedCustomerInfo()
    if (res) {
        isNeedCustomerInfo.value = true
    }
    return res
}
const getIsPickUpStore = async () => {
    const res = await getPickUpStore()
    if (res) {
        isPickUpChange.value = res
    }
    return res
}
onMounted(async () => {
    const orderInfo = JSON.parse(decodeURIComponent(props.orderInfo)) || {};
    console.log("orderInfo", orderInfo);
    try {
       await getIsNeedCustomerInfo() 
       await getIsPickUpStore() 
    } catch (error) {
        
    }
    Object.assign(tempVal, {
        cartItemDTOList: orderInfo.cartItemDTOList || [],
        money: orderInfo.money || 0,
        goodsAmount: orderInfo.goodsAmount || 0,
        totalPoints: orderInfo.totalPoints || 0,
        shippingFee: orderInfo.shippingFee || 0,
        returnPoints: orderInfo.returnPoints || 0,
        requestNo: orderInfo.requestNo || null,
        totalCoupons: orderInfo.totalCoupons || 0,
        cashCouponAmt: orderInfo.cashCouponAmt || 0,
        maxCashCouponAmt: orderInfo.maxCashCouponAmt || 0,
        selectedCashCouponIdList: orderInfo.selectedCashCouponIdList || [],
        storeEntity: orderInfo.storeEntity || {},
        selectIds: orderInfo.selectIds || orderInfo.selectedCashCouponIdList || [],
    });
    if(tempVal.maxCashCouponAmt>0){
        getUseCouponList(tempVal.maxCashCouponAmt);
    }

    // 收货地址
    if (orderInfo?.customerAddressDTO && isObject(orderInfo?.customerAddressDTO)) {
        const {
            name,
            mobile,
            companyId,
            company,
            provinceId,
            province,
            cityId,
            cityName,
            areaId,
            area,
            townId,
            town,
            address,
            isDefault,
            csWxNickname
        } = orderInfo?.customerAddressDTO;
        Object.assign(defaultAddress, {
            name,
            mobile,
            companyId,
            company,
            provinceId,
            province,
            cityId,
            cityName,
            areaId,
            area,
            townId,
            town,
            address,
            isDefault,
            csWxNickname
        });
    }
});


onActivated(() => {
    console.log("currentAddressInfo", currentAddressInfo.value);
    if (currentAddressInfo.value && isObject(currentAddressInfo.value)) {
        const {
            name,
            mobile,
            companyId,
            company,
            provinceId,
            province,
            cityId,
            cityName,
            areaId,
            area,
            townId,
            town,
            address,
            isDefault,
            csWxNickname
        } = currentAddressInfo.value;
        Object.assign(defaultAddress, {
            name,
            mobile,
            companyId,
            company,
            provinceId,
            province,
            cityId,
            cityName,
            areaId,
            area,
            townId,
            town,
            address,
            isDefault,
            csWxNickname
        });
    }
    deleteKeepAliveRouteByName(KeepAliveRouteNameEnum.CONFIRM_ORDER);
});

/** 组件卸载 */
onUnmounted(() => {
    currentAddressInfo.value = null;
});
</script>

<style scoped lang="less">
@import url('@/styles/storeVar.less');

.coupon {
    color: #999999;
    font-size: 12px;
    margin-top: 3px;
}

.confirm-order {
    padding: 12px;
    box-sizing: border-box;
    overflow: auto;
    height: calc(100vh - env(safe-area-inset-bottom) - 80px - 24px);

    .card-box {
        padding: 10px 12px;
        box-sizing: border-box;
        margin-bottom: 8px;
        background-color: #fff;
        border-radius: 8px;

        .card-header {
            font-size: 16px;
        }

        .address-content {
            padding: 20px 0 12px 0;
            margin-bottom: 8px;
            border-bottom: none;
            background: linear-gradient(90deg, #EEEEEE 0%, #EEEEEE 50%, transparent 50%, transparent 100%) bottom repeat-x;
            background-size: 8px 1px;
            display: flex;

            .content-img {
                width: 56px;
                height: 56px;
                border-radius: 4px;
                margin-right: 8px;
            }

            .content-info {
                .info-title {
                    font-size: 16px;
                }

                .info-footer {
                    .footer-info {
                        margin-top: 4px;
                        display: flex;
                        align-items: center;
                        margin-bottom: 5px;
                        display: flex;
                        align-items: center;

                        img {
                            width: 16px;
                            height: 16px;
                            margin-right: 4px;
                        }

                        .desc {
                            display: flex;
                            font-size: 12px;

                            .desc-left {
                                margin-right: 5px;
                            }
                        }
                    }
                }
            }
        }
        .no-options-available {
             display: flex;
             align-items: center;
             width: 100%;
             height: 70px;
             margin: 16px 0 24px 0;
             background-image: url('@/assets/storeImage/product/confirmOrder/self_pick-up_site_bg.png');
             background-repeat: no-repeat;
             background-size: 100% ;
             padding-left: 16px;
             color: #1677FF;
             box-sizing: border-box;
             .address_icon_blue {
                width: 20px;
                height: 20px;
                margin-right: 2px;
             }
             .right_icon {
                width: 16px;
                height: 16px;
             }
        }
        .goods-content {
            display: flex;
            margin: 20px 0 12px 0;

            img {
                width: 80px;
                height: 80px;
                border-radius: 4px;
                margin-right: 8px;
            }

            .goods-info {
                flex: 1;

                .info-title {}

                .info-desc {
                    font-size: 14px;
                    color: #999999;
                    margin: 4px 0;
                }

                .info-price {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;

                    .price-num {
                        font-size: 14px;
                        color: #999999;
                    }
                }
            }
        }

        .desc-content {
            .desc-info {
                border-bottom: 1px dashed #EEEEEE;

                .info {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    margin-bottom: 12px;

                    .info-left {
                        font-size: 16px;
                    }

                    .info-right {
                        display: flex;
                        align-items: center;
                    }
                }
            }

            .desc-sum {
                display: flex;
                align-items: baseline;
                justify-content: flex-end;
                margin-top: 8px;

                .sum-name {
                    font-size: 14px;
                    color: #666666;
                }
            }
        }

        .returnPoints {
            display: flex;
            align-items: center;
            background: #fff;
            padding: 12px 0;
            border-radius: 8px;
            color: #666666;
            font-size: 14px;

            .coins {
                width: 16px;
                height: 16px;
            }

            .number {
                font-family: "DINPro";
                color: @error-color;
                font-weight: bold;
            }
        }


        .card-footer {
            font-size: 12px;
            line-height: 18px;
            color: #999999;
        }
    }
    .confirm-info.card-box {
        .card-header {
            font-weight: 700;
            margin-bottom: 8px;
        }
        :deep(.van-cell-group) {
            margin: 0;
            .van-cell {
                padding: 10px 0;
                .van-cell__title {
                    display: flex;
                    align-items: center;
                    width: 52px;
                    &::before {
                        margin-top: 4px;
                    }
                }
                .van-field__control {
                    width: 259px;
                    height: 40px;
                    background: #F8F8F8;
                    border-radius: 4px;
                    padding: 0 16px;
                }
                &::after {
                    display: none;
                }
            }
        }
    }
    .footer {
        position: fixed;
        bottom: calc(24px + env(safe-area-inset-bottom));
        width: calc(100% - 24px);
        padding: 12px;
        box-sizing: border-box;
        z-index: 999;

        .footer-content {
            display: flex;
            background-color: #fff;
            border-radius: 33px;
            overflow: hidden;
            box-shadow: 0 3px 24px 0 rgba(172, 172, 172, 0.4);


            .footer-left {
                flex: 1;
                display: flex;
                align-items: center;
                padding: 12px 7px 12px 24px;

                .left-content {
                    flex: 1;

                    .content {
                        display: flex;
                        align-items: center;

                        .footer-price {
                            display: flex;
                            color: @error-color;

                            .welfare {
                                flex: 1;
                                display: flex;
                                align-items: center;
                                font-size: 16px;
                                font-weight: bold;
                                display: flex;
                                align-items: baseline;

                                .price {
                                    margin-right: 5px;
                                    font-size: 20px;
                                }

                                .desc {
                                    font-size: 14px;
                                }
                            }

                        }
                    }
                }

            }

            img {
                margin-right: 12px;
                width: 32px;
                height: 32px;
            }


            .footer-right {
                display: flex;
                font-size: 16px;
                display: flex;
                align-items: center;
                justify-content: center;
                background-color: @error-color;
                color: #fff;
                width: 96px;
                font-weight: 500;

                .footer-text {
                    color: #666666;
                    padding-top: 4px;
                    font-size: 16px;
                }

                button::after {
                    border: none;
                }
            }

            .login-tip {
                font-weight: bold;
                font-size: 14px;
            }
        }
    }

}
</style>
