<template>
  <VanPopup
    :show="showRef"
    @update:show="handleUpdateShow"
    round
    position="bottom"
    closeable
    safe-area-inset-bottom
    close-icon="close"
    teleport="body"
    style="height: auto;"
  >
    <div class="wrapper">
      <!-- 订单类型 -->
      <div class="form-item" v-if="props.isOrderType">
        <span>订单类型</span>
        <VanPopover v-model:show="showPopover" :show-arrow="false">
          <template #reference>
            <div class="order-type">
              <span class="title">{{ orderTypeLabel }}</span>
              <img :src="pullDownSrc" alt="" class="order-type-icon" :class="{ 'rotate-180': showPopover }" />
            </div>
          </template>
          <div style="width: calc(100vw - 24px);padding: 12px;box-sizing: border-box;">
            <div
              v-for="item in OrderTypeOptions"
              :key="item.value"
              class="order-type-item"
              @click="handleOrderTypeChange(item)"
            >
              <span :class="{ 'order-type-active': item.value === formValue.orderType }">{{ item.label }}</span>
            </div>
          </div>
        </VanPopover>
      </div>
      <!-- 归属店员 -->
      <div class="form-item">
        <span>归属店员</span>
        <VanField
          v-model="formValue.staffShortId"
          rows="1"
          autosize
          type="textarea"
          placeholder="请输入店员编号"
          :maxlength="200"
          style="flex: 1;background: #F8F8F8;padding: 8px;border-radius: 4px;"
        />
      </div>
      <!-- 归属会员 -->
      <div class="form-item">
        <span>归属会员</span>
        <VanField
          v-model="formValue.memberShortId"
          rows="1"
          autosize
          type="textarea"
          placeholder="请输入会员编号"
          :maxlength="200"
          style="flex: 1;background: #F8F8F8;padding: 8px;border-radius: 4px;"
        />
      </div>
      <div v-if="props.isProductName" class="form-item">
        <span>商品名称</span>
        <VanField
          v-model="formValue.productName"
          rows="1"
          autosize
          type="textarea"
          placeholder="请输入商品名称"
          :maxlength="200"
          style="flex: 1;background: #F8F8F8;padding: 8px;border-radius: 4px;"
        />
      </div>
      <!-- 按钮 -->
      <VanRow justify="space-between" align="center" :gutter="10" class="footer">
        <VanCol span="12">
          <VanButton type="default" round block @click="handleReset">重置</VanButton>
        </VanCol>
        <VanCol span="12">
          <VanButton type="danger" round block @click="handleConfirm">查询</VanButton>
        </VanCol>
      </VanRow>
    </div>
  </VanPopup>
</template>

<script lang="ts" setup>
import { ref, computed, toRefs, watch } from "vue";
import { OrderStatisticsTypeEnum } from "@/views/StoreModule/enums";
/** 静态资源 */
import pullDownSrc from "@/assets/storeImage/storeHome/pull-down.png";

defineOptions({ name: 'JStatisticalScreening' });

export interface ModelType {
  orderType?: OrderStatisticsTypeEnum;
  staffShortId?: string;
  memberShortId?: string;
  productName?: string;
}

/** props */
const props = withDefaults(defineProps<{
  show: boolean;
  /** 是否显示订单类型 */
  isOrderType?: boolean;
  isProductName?: boolean;
  model: ModelType
}>(), {
  isOrderType: true,
  isProductName: true
});

/** emit */
const emit = defineEmits<{
  (e: 'update:show', val: boolean): void;
  (e: 'confirm', val: ModelType): void;
}>();

const showRef  = computed({
  get: () => props.show,
  set: (val) => emit('update:show', val)
});

/** 表单参数 */
const initparams = {
  orderType: OrderStatisticsTypeEnum.ALL,
  staffShortId: null,
  memberShortId: null,
  productName:undefined
};
const formValue = ref<ModelType>({ ...initparams });

/** 订单类型 */
const OrderTypeOptions = [
  { label: '全部', value: OrderStatisticsTypeEnum.ALL },
  { label: '购物类', value: OrderStatisticsTypeEnum.SHOPPING },
  { label: '福利券兑换', value: OrderStatisticsTypeEnum.COUPON },
  { label: '积分兑换', value: OrderStatisticsTypeEnum.INTEGRAL },
];
const orderTypeLabel = computed(() => {
  const orderType = OrderTypeOptions.find((item) => item.value === formValue.value.orderType);
  return orderType?.label || '';
})

const showPopover = ref(false);

function handleUpdateShow(val: boolean) {
  emit('update:show', val);
}

function handleOrderTypeChange(item) {
  formValue.value.orderType = item.value;
  showPopover.value = false;
}

function handleReset() {
  formValue.value = { ...initparams };
}

function handleConfirm() {
  emit('update:show', false);
  emit('confirm', formValue.value);
}

/** 监听 */
watch(() => props.model, (newVal) => {
  if (newVal) {
    formValue.value = { ...newVal };
  }
}, { immediate: true });
</script>

<style lang="less" scoped>
.wrapper {
  padding: 46px 12px 8px 12px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  .form-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
    span {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 18px;
        color: #333333;
        line-height: 26px;
        text-align: left;
        font-style: normal;
        text-transform: none;
    }
    .order-type {
        height: 40px;
        background: #F8F8F8;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px;
        box-sizing: border-box;
        .title {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 400;
            font-size: 16px;
            color: #333333;
            text-align: left;
            font-style: normal;
            text-transform: none;
        }
        .order-type-icon {
            width: 16px;
            height: 16px;
        }
    }
  }
  .footer {
    margin-top: 24px;
  }
}
.order-type-item {
    width: 100%;
    padding: 8px 12px;
    span {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #666666;
        line-height: 20px;
        font-style: normal;
        text-transform: none;
    }
    .order-type-active {
        color: #EF1115 !important;
    }
}
.rotate-180 {
    transform: rotate(-180deg);
}
:deep(.van-button__text) {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  text-align: center;
  font-style: normal;
  text-transform: none;
}
:deep(.van-field__control) {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 16px;
  color: #333333;
  text-align: left;
  font-style: normal;
  text-transform: none;
}
</style>
