<template>
  <JLoadingWrapper :show="isPageLoadingRef">
    <div class="store_order_statistics">
      <!-- 通知 -->
      <div class="statistics-notice">
        <VanNoticeBar
          left-icon="volume-o"
          color="#1677FF"
          background="#ECF5FF"
          style="height: 36px;"
        >
          <p class="notice-title">以下统计数据为已锁单且没有发起退款的订单数据</p>
        </VanNoticeBar>
      </div>
      <!-- 筛选条件 -->
      <div class="screening_condition_wrapper">
        <div class="screening_condition_header">
          <TagSelectGroup :data="dataRangeList" v-model:value="model.dataRangeRef" />
          <div class="screening_condition_header_right" @click="onShowScreening">
            <span>筛选</span>
            <SvgIcon
              name="dropDown"
              style="font-size: 18px;"
              :class="{'rotate-180': isShowStatisticalScreening, 'icon': true }"
            />
          </div>
          <!-- 导出 -->
          <VanButton
            type="danger"
            size="small"
            style="width: 56px;height: 24px;"
            @click="onExportData"
          >
            导出
          </VanButton>
        </div>
        <!-- 时间筛选 -->
        <JDateRangePicker v-model:value="model.timeRef" />
      </div>
      <!-- 订单信息 -->
      <div class="store_order_statistics_info">
        <!-- 表头 -->
        <div class="store_order_statistics_info_header">
          <div class="store_order_statistics_info_header_item" style="width: 96px; padding-left: 8px;">商品名</div>
          <div class="store_order_statistics_info_header_item" style="flex: 1;">商品总数</div>
          <div class="store_order_statistics_info_header_item" style="flex: 1;">订单数</div>
          <div class="store_order_statistics_info_header_item" style="flex: 1;">待核销订单数</div>
          <div class="store_order_statistics_info_header_item" style="flex: 1;">待核销商品数</div>
        </div>
        <!-- 表体 -->
        <div class="store_order_statistics_info_body">
          <VanPullRefresh v-model="refreshingRef" @refresh="onRefresh" class="store_order_list_content">
            <template v-if="ordersList.length">
              <VanList
                v-model:loading="isLoadingRef"
                :finished="isFinishedRef"
                finished-text="没有更多了"
                @load="onLoad"
              >
                <StoreOrderStatisticsCard
                  v-for="item,index in ordersList"
                  :key="index"
                  :orderInfo="item"
                  class="store_order_list_item"
                />
              </VanList>
            </template>
            <template v-else>
              <EmptyData />
            </template>
          </VanPullRefresh>
        </div>
      </div>
      <!-- 订单统计筛选 -->
      <JStatisticalScreening v-model:show="isShowStatisticalScreening" :model="model" @confirm="onConfirm" />
    </div>
  </JLoadingWrapper>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import dayjs from "dayjs";
import { showToast } from "vant";
import { RoutesName } from "@/enums/routes";
import { useRouterUtils } from "@/views/StoreModule/hooks";
import { useMessages } from "@/hooks/useMessage";
import { StoreDataExportEnum, OrderStatisticsTypeEnum } from "@/views/StoreModule/enums";
import useGetStoreOrderStatistics from "./hooks/useGetStoreOrderStatistics";
import { exportRecordApi } from "@/services/storeApi";
/** 相关组件 */
import TagSelectGroup from "@/views/StoreModule/components/TagSelectGroup.vue";
import JDateRangePicker from "@/views/StoreModule/components/JDateRangePicker.vue";
import JStatisticalScreening, { type ModelType } from "@/views/StoreModule/components/JStatisticalScreening.vue";
import EmptyData from "@/views/StoreModule/components/EmptyData.vue";
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";
import StoreOrderStatisticsCard from "./components/StoreOrderStatisticsCard.vue";

defineOptions({ name: 'StoreOrderStatistics' });

const { createMessageError } = useMessages();
const { routerPushByRouteName } = useRouterUtils();
const {
  model,
  dataRangeList,
  isPageLoadingRef,
  ordersList,
  isFinishedRef,
  refreshingRef,
  isLoadingRef,
  onLoad,
  onRefresh,
  getStoreOrderStatistics,
  initStoreOrderStatistics
} = useGetStoreOrderStatistics();

const isShowStatisticalScreening = ref(false);

function onShowScreening() {
  isShowStatisticalScreening.value = !isShowStatisticalScreening.value;
}

/** 筛选确认回调 */
function onConfirm(data: ModelType) {
  const { orderType, staffShortId, memberShortId, productName } = data;
  Object.assign(model.value, {
    orderType,
    staffShortId,
    memberShortId,
    productName
  });

  initStoreOrderStatistics();
}

/** 导出数据 */
async function onExportData() {
  try {
    const { timeRef, orderType, productName, staffShortId, memberShortId } = model.value;
    const [startTime, endTime] = timeRef;
    let params = {
      type: StoreDataExportEnum.D00_ORDERS,
      startTime: dayjs(startTime).format("YYYY-MM-DD 00:00:00"),
      endTime: dayjs(endTime).format("YYYY-MM-DD 23:59:59"),
      orderType: orderType === OrderStatisticsTypeEnum.ALL ? undefined : orderType,
      productName,
      staffShortId,
      memberShortId
    };

    const resp = await exportRecordApi(params);
    if (resp) {
      showToast(`导出订单统计成功`);

      toExportRecord();
    }
  } catch (error) {
    createMessageError(`导出失败：${error}`);
  }
}

/** 跳转导出记录 */
function toExportRecord() {
  routerPushByRouteName(RoutesName.StoreExportRecord);
}

/** 组件挂载 */
onMounted(() => {
  initStoreOrderStatistics();
});
</script>

<style lang="less" scoped>
.store_order_statistics {
  width: 100%;
  height: 100%;
  background: #F8F8F8;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;

  .statistics-notice {
    height: 36px;
    flex-shrink: 0;
    .notice-title {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #1677FF;
      line-height: 22px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }

  .screening_condition_wrapper {
    height: 96px;
    background: #FFFFFF;
    padding: 12px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex-shrink: 0;

    .screening_condition_header {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .screening_condition_header_right {
        display: flex;
        align-items: center;

        span {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          font-size: 14px;
          color: #666666;
          line-height: 20px;
          text-align: right;
          font-style: normal;
          text-transform: none;
        }

        .icon {
          transition: all 0.3s;
        }
      }
    }
  }

  .store_order_statistics_info {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #FFFFFF;
    margin-top: 8px;
    box-sizing: border-box;

    .store_order_statistics_info_header {
      height: 60px;
      display: flex;
      align-items: center;
      border-bottom: 1px solid #EEEEEE;
      padding: 12px;
      box-sizing: border-box;

      .store_order_statistics_info_header_item {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 13px;
        color: #666666;
        line-height: 18px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }

    .store_order_statistics_info_body {
      flex: 1;
      box-sizing: border-box;

      .store_order_list_content {
        height: calc(100vh - 200px - env(safe-area-inset-bottom));
        padding: 0px 12px 12px 12px;
        box-sizing: border-box;
        overflow-y: auto;
      }

      .store_order_list_item:nth-child(2n) {
        background: #FFF6F6;
        border-radius: 8px;
      }
    }
  }
}

.rotate-180 {
  transform: rotate(-180deg);
}
</style>
