<template>
  <VanDialog
    v-model:show="showRef"
    title="提示"
    show-cancel-button
    confirm-button-text="确定核销"
    confirm-button-color="#EF1115"
    @confirm="confirm"
  >
    <div class="wrapper">
      <span v-if="props.isBatch">
        请确认把选中的
        <span class="batch-count">{{ props.batchCount }}</span>
        张订单信息无误后进行一键核销
      </span>
      <span v-else>请确认订单信息无误后核销</span>
    </div>
  </VanDialog>
</template>

<script lang="ts" setup>
import { ref, computed } from "vue";

defineOptions({ name: 'WriteOffOrder' });

/** props */
const props = withDefaults(defineProps<{
  show: boolean;
  /** 是否批量核销 */
  isBatch?: boolean;
  /** 批量核销数量 */
  batchCount?: number;
}>(), { isBatch: false });

/** emits */
const emits = defineEmits<{
  (e: 'update:show', show: boolean): void
  (e: 'confirm', isBatch?: boolean): void
}>();

const showRef = computed({
  get() {
    return props.show;
  },
  set(show: boolean) {
    emits('update:show', show);
  }
});

function confirm() {
  emits('confirm', props.isBatch);
}
</script>

<style lang="less" scoped>
.wrapper {
  padding: 16px 24px;
  display: flex;
  justify-content: center;
  align-items: center;

  span {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 16px;
    color: #666666;
    line-height: 24px;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }
  .batch-count {
    color: #ff0000;
  }
}
</style>
