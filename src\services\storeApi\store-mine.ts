import { defHttp } from "@/services";
import { getStoreApiUrl } from "@/utils/http/urlUtils";

/** 门店管理 */
export const enum storeManagementApiEnum {
  // 门店管理-订单统计
  orderListStat = "/h5/store/personal/orderListStat",
  // 生成门店客户邀请链接
  storeCsLink = "/h5/store/personal/storeCsLink",
  // 门店管理-店员/店长订单概览数据统计
  staffSurveyStatData = "/h5/store/personal/staffSurveyStatData",
  // 我的订单列表
  orderList = "/h5/order/queryOrderByStatus",
  // 统计我的订单
  myOrderStat = "/h5/order/countMyOrders",
  // 订单详情
  orderDetail = "/h5/order/getOrderDetail",
  // 用户信息
  storeUserInfo = "/h5/store/personal/curUserInfo",
  // 查询我的订单核销码
  getMyVerifyCode = "/h5/orderVerification/getMyVerifyCode",
  // 我的-福利券张数&积分余额
  myCouponAndIntegral = "/h5/store/personal/getCouponAndPointsStats",
  // 核销订单
  verifyOrder = "/h5/order/verify",
  // 门店管理-会员排行榜
  leaderboardByOrder = "/h5/store/personal/leaderboardForMember",
  // 门店管理-店长排行榜
  leaderboardByStore = "/h5/store/personal/leaderboardForStoreManager",
  // 待核销订单列表
  queryToShipOrder = "/h5/order/queryToShipOrder",
  // 店铺订单
  queryStoreOrder = "/h5/order/queryStoreOrder",
  // 积分明细
  queryPointsDetail = "/h5/point/pointsRecord",
  // 积分余额展示
  queryPointsBalance = "/h5/point/getAvailPoints",
  // 加减积分
  addOrDecPoints = "/h5/point/addOrDecPoints",
  // 获取用户UnionId
  getUnionId = "/H5/QrCode/getQrCodeUnionId",
  // 查询我的售后列表
  queryMyAfterSaleList = "/h5/afterSaleRecord/getMyList",
  // 查询店铺订单售后列表
  queryStoreAfterSaleList = "/h5/afterSaleRecord/getStoreList",
  // 查询我的售后记录
  queryMyAfterSaleRecord = "/h5/afterSaleRecord/getMyRecord",
  // 取消订单
  cancelOrder = "/h5/order/cancelOrder",
  // 查询物流信息
  queryLogisticsInfo = "/h5/order/traces/list",
  // 确认收货 
  confirmReceipt = "/h5/order/confirmSign",
  // 获取会员附近自提门店分页列表
  getNearbyStoreBasicInfoPage = "/h5/storeEntity/getNearbyStoreBasicInfoPage",
  // 批量核销订单
  batchVerify = '/h5/order/batchVerify'
}

/**
 * @description 批量核销订单
 */
export function batchVerify(orderIds: Array<{
  code: string;
  verifyCode: string;
}>) {
  return defHttp.post({
    url: getStoreApiUrl(storeManagementApiEnum.batchVerify),
    params: {
      data: {
        verifyVOS: orderIds,
      }
    },
    requestConfig: {
      skipCrypto: true,
    },
  });
}

interface OrderListStatPage {
  data: {
    /** 排序类型: 1消费金额 2下单数量 */
    type?: 1 | 2;
    /** 订单类型: 0全部 1购物类 2福利券 3积分兑换 */
    orderType?: 0 | 1 | 2 | 3;
    /** 门店id */
    storeId?: string;
    /** 归属店员id */
    staffId?: string;
    /** 归属会员id */
    memberId?: string;
    /** 开始时间 */
    startTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 店员id列表 */
    staffIds?: string[];
  };
  pageVO: {
    current: number;
    size: number;
  };
}
/**
 * @description 订单统计
 */
export function getOrderListStat(_params: OrderListStatPage) {
  return defHttp.post({
    url: getStoreApiUrl(storeManagementApiEnum.orderListStat),
    params: _params,
    requestConfig: {
      skipCrypto: true,
    },
  });
}

/**
 * @description 生成门店客户邀请链接
 */
export function getStoreCsLink() {
  return defHttp.get({
    url: getStoreApiUrl(storeManagementApiEnum.storeCsLink),
    requestConfig: {
      skipCrypto: true,
    },
  });
}

/**
 * @description 订单数据概览
 */
export function getOrderDataOverview(_params) {
  return defHttp.post({
    url: getStoreApiUrl(storeManagementApiEnum.staffSurveyStatData),
    params: {
      data: _params,
    },
    requestConfig: {
      skipCrypto: true,
    },
  });
}

/**
 * @description 我的订单列表
 */
export function getMyOrderList(_params) {
  return defHttp.post({
    url: getStoreApiUrl(storeManagementApiEnum.orderList),
    params: _params,
    requestConfig: {
      skipCrypto: true,
    },
  });
}

/**
 * * @description 我的售后订单列表
 */
export function getMyAfterSaleOrderList(_params) {
  return defHttp.post({
    url: getStoreApiUrl(storeManagementApiEnum.queryMyAfterSaleList),
    params: _params,
    requestConfig: {
      skipCrypto: true,
    },
  });
}

/**
 * * @description 店铺售后订单列表
 */
export function getStoreAfterSaleOrderList(_params) {
  return defHttp.post({
    url: getStoreApiUrl(storeManagementApiEnum.queryStoreAfterSaleList),
    params: _params,
    requestConfig: {
      skipCrypto: true,
    },
  });
}

/**
 * @description 我的售后订单详情
 */
export function getMyAfterSaleOrderDetail(_params) {
  return defHttp.get({
    url: getStoreApiUrl(storeManagementApiEnum.queryMyAfterSaleRecord),
    params: _params,
    requestConfig: {
      skipCrypto: true,
      isQueryParams: true,
    },
  });
}

/**
 * @description 统计我的订单
 */
export function getMyOrderStat() {
  return defHttp.get({
    url: getStoreApiUrl(storeManagementApiEnum.myOrderStat),
    requestConfig: {
      skipCrypto: true,
    },
  });
}

/**
 * @description 获取订单信息
 */
export function getOrderDetailByOrderCode(_params) {
  return defHttp.get({
    url: getStoreApiUrl(storeManagementApiEnum.orderDetail),
    params: _params,
    requestConfig: {
      skipCrypto: true,
      isQueryParams: true,
    },
  });
}

/**
 * @description 获取用户信息
 */
export function getStoreUserInfo(_params = {}) {
  return defHttp.get({
    url: getStoreApiUrl(storeManagementApiEnum.storeUserInfo),
    params: _params,
    requestConfig: {
      skipCrypto: true,
      isQueryParams: true,
    },
  });
}

/**
 * @description 根据订单code获取我的订单核销码
 */
export function getMyOrderVerifyCode(_params) {
  return defHttp.get({
    url: getStoreApiUrl(storeManagementApiEnum.getMyVerifyCode),
    params: _params,
    requestConfig: {
      skipCrypto: true,
      isQueryParams: true,
    },
  });
}

/**
 * @description 我的-福利券张数&积分余额
 */
export function getMyCouponAndIntegral() {
  return defHttp.get({
    url: getStoreApiUrl(storeManagementApiEnum.myCouponAndIntegral),
    requestConfig: {
      skipCrypto: true,
      isQueryParams: true,
    },
  });
}

/**
 * @description 订单核销
 */
export function verifyOrder(_params) {
  return defHttp.post({
    url: getStoreApiUrl(storeManagementApiEnum.verifyOrder),
    params: {
      data: _params,
    },
    requestConfig: {
      skipCrypto: true,
    },
  });
}

/**
 * @description 待核销订单列表 
 */
export function getVerifyOrderList(_params) {
  return defHttp.post({
    url: getStoreApiUrl(storeManagementApiEnum.queryToShipOrder),
    params: _params,
    requestConfig: {
      skipCrypto: true,
    },
  });
}

/**
 * @description 店铺订单列表
 */
export function getStoreOrderList(_params) {
  return defHttp.post({
    url: getStoreApiUrl(storeManagementApiEnum.queryStoreOrder),
    params: _params,
    requestConfig: {
      skipCrypto: true,
    },
  });
}

/**
 * @description 门店管理-排行榜
 */
export function leaderboardByOrder(_params) {
  return defHttp.post({
    url: getStoreApiUrl(storeManagementApiEnum.leaderboardByOrder),
    params: _params,
    requestConfig: {
      skipCrypto: true,
    }
  });
}

/**
 * @description 门店管理-店长排行榜
 */
export function leaderboardByStore(_params) {
  return defHttp.post({
    url: getStoreApiUrl(storeManagementApiEnum.leaderboardByStore),
    params: _params,
    requestConfig: {
      skipCrypto: true,
    }
  });
}

/**
 * @description 积分明细
 */
export function getIntegralDetail(_params) {
  return defHttp.post({
    url: getStoreApiUrl(storeManagementApiEnum.queryPointsDetail),
    params: _params,
    requestConfig: {
      skipCrypto: true,
    },
  });
}

/**
 * @description 积分余额展示
 */
export function getIntegralBalance(_params = {}) {
  return defHttp.post({
    url: getStoreApiUrl(storeManagementApiEnum.queryPointsBalance),
    params: {
      data: _params,
    },
    requestConfig: {
      skipCrypto: true,
      isQueryParams: true,
    },
  });
}

/**
 * @description 加减积分
 */
export function addOrSubtractIntegral(_params) {
  return defHttp.post({
    url: getStoreApiUrl(storeManagementApiEnum.addOrDecPoints),
    params: {
      data: _params,
    },
    requestConfig: {
      skipCrypto: true,
    },
  });
}

/**
 * @description 获取用户UnionId
 */
export function getUserUnionId(_params={}) {
  return defHttp.get({
    url: getStoreApiUrl(storeManagementApiEnum.getUnionId),
    params: _params,
    requestConfig: {
      skipCrypto: true,
      isQueryParams: true,
    },
  });
}

/**
 * @description 取消订单
 */
export function cancelOrder(orderCode: string) {
  return defHttp.post({
    url: getStoreApiUrl(storeManagementApiEnum.cancelOrder),
    params: {
      data: orderCode,
    },
    requestConfig: {
      skipCrypto: true,
    },
  });
}

/**
 * @description 查询订单物流信息
 */
export function queryOrderLogistics(_params: {
  trackingNo: string;
  orderCode: string;
  shipCompanyCode: string;
}) {
  return defHttp.post({
    url: getStoreApiUrl(storeManagementApiEnum.queryLogisticsInfo),
    params: {
      data: _params,
    },
    requestConfig: {
      skipCrypto: true,
    },
  });
}

/**
 * @description 确认收货
 */
export function confirmReceipt(orderCode: string) {
  return defHttp.put({
    url: getStoreApiUrl(storeManagementApiEnum.confirmReceipt + `?orderCode=${orderCode}`),
    requestConfig: {
      skipCrypto: true,
    },
  });
}
/**
 * @description 获取会员附近自提门店分页列表
 */
export function getNearbyStoreBasicInfoPage(_params) {
  return defHttp.post({
    url: getStoreApiUrl(storeManagementApiEnum.getNearbyStoreBasicInfoPage),
    params:  _params,
    requestConfig: {
      skipCrypto: true,
    },
  });
}