<template>
  <div class="pck-up-location-page">
    <JLoadingWrapper :show="isPageLoadingRef">
      <van-search
        @search="handleSearch"
        @clear="handleSearch"
        @click-left-icon="handleSearch"
        shape="round"
        v-model="modal.keyword"
        placeholder="请输入门店名称"
      />
      <!-- 下拉刷新区域 -->
      <van-pull-refresh class="content-wrapper" v-model="refreshingRef" @refresh="onRefresh">
        <!-- 列表区域 -->
        <van-list
          v-if="pckUpLocationList.length"
          v-model:loading="isLoadingRef"
          @load="onLoad"
          :finished="isFinishedRef"
          finished-text="没有更多了"
        >
          <!-- 列表卡片组件 -->
          <div
            v-for="(item, index) in pckUpLocationList"
            @click="selected = item.shortId"
            :key="index"
            :class="{ 'card-item': true, active: selected === item.shortId }"
          >
            <img class="content-img" :src="item.storeAvatar || storeSrc" alt="" srcset="" />
            <div class="content-info">
              <div class="info-title">{{ item.storeName || "--" }}</div>
              <div class="info-footer">
                <div class="footer-info">
                  <img :src="addressIcon" alt="" srcset="" />
                  <div class="desc">
                    {{
                      `${item.province || "--"}${item.city || "--"}${item.area || "--"}${item.addressDetail || "--"}`
                    }}
                  </div>
                </div>
                <div class="footer-info">
                  <img :src="phoneIcon" alt="" srcset="" />
                  <div class="desc">
                    <div class="desc-left">{{ item.contactName || "--" }}</div>
                    <div class="desc-right">{{ item.contactPhone || "--" }}</div>
                  </div>
                </div>
              </div>
            </div>
            <img v-if="selected === item.shortId" class="selected" :src="selectedIcon" alt="" />
          </div>
          <!-- <div
            v-for="(item, index) in 30"
            @click="selected = index"
            :key="index"
            :class="{ 'card-item': true, active: selected === index }"
          >
            <img class="content-img" src="https://img.yzcdn.cn/vant/cat.jpeg" alt="" srcset="" />
            <div class="content-info">
              <div class="info-title">东英科技园店</div>
              <div class="info-footer">
                <div class="footer-info">
                  <img :src="addressIcon" alt="" srcset="" />
                  <div class="desc">广州市天河区体育东路21号</div>
                </div>
                <div class="footer-info">
                  <img :src="phoneIcon" alt="" srcset="" />
                  <div class="desc">
                    <div class="desc-left">陈先生</div>
                    <div class="desc-right">13800138000</div>
                  </div>
                </div>
              </div>
            </div>
            <img v-if="selected === index" class="selected" :src="selectedIcon" alt="" />
          </div> -->
        </van-list>

        <!-- 空状态显示 -->
        <van-empty v-else description="暂无数据" :image="emptyImg" :image-size="[200, 200]" />
      </van-pull-refresh>
      <view class="footer-box">
        <van-button
          round
          class="btn"
          :disabled="isPageLoadingRef || !selected || !pckUpLocationList.length"
          @click="handleSave"
        >
          确定
        </van-button>
      </view>
    </JLoadingWrapper>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from "vue";
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";
import { useMessages } from "@/hooks/useMessage";
import addressIcon from "@/assets/storeImage/product/confirmOrder/addressIcon.png";
import phoneIcon from "@/assets/storeImage/product/confirmOrder/phoneIcon.png";
import selectedIcon from "@/assets/storeImage/product/confirmOrder/selected.png";
import storeSrc from "@/assets/storeImage/storeMine/store.png";
import { getNearbyStoreBasicInfoPage } from "@/services/storeApi";
import emptyImg from "@/assets/store0602Image/emptyImg.png";
import { useRouter } from "vue-router";
import { RoutesName } from "@/enums/routes";
import { StoreGoodsEnum } from "@/enums/storeGoods";
const isPageLoadingRef = ref(false);
const router = useRouter();
/** 初始化查询参数 */
const initParams = {
  keyword: null,
};
/** 分页 */
const pageVO = reactive({
  size: 30,
  current: 1,
  total: 0,
});
const props = withDefaults(
  defineProps<{
    orderInfo: any;
    type: StoreGoodsEnum
  }>(),
  {
    orderInfo: () => ({}),
  },
);
/** 是否加载完 */
const isFinishedRef = ref(true);
/** 加载 */
const isLoadingRef = ref(false);
/** 刷新 */
const refreshingRef = ref(false);
const modal = ref({ ...initParams });
const { createMessageSuccess, createMessageError } = useMessages();
const pckUpLocationList = ref([]);
const selected = ref(null);
const _orderInfo = ref({
  cartItemDTOList: [],
  selectedCashCouponIdList: [],
  selectIds: [],
  money: 0,
  goodsAmount: 0,
  totalPoints: 0,
  shippingFee: 0,
  returnPoints: 0,
  requestNo: null,
  totalCoupons: 0,
  maxCashCouponAmt: 0,
  cashCouponAmt: 0,
  storeEntity: {},
});
/** 加载数据 */
function onLoad() {
  if (pageVO.current * pageVO.size < pageVO.total) {
    isLoadingRef.value = true;
    pageVO.current++;
    getPckUpLocationList();
  }
}
/** 初始化 */
function ininParams() {
  pageVO.current = 1;
  pageVO.total = 0;
  isFinishedRef.value = false;
}
/** 刷新 */
function onRefresh() {
  ininParams();
  // 重新加载数据
  refreshingRef.value = true;
  getPckUpLocationList();
}
/** 获取搜索参数 */
function getSearchParams() {
  const _searchParams = {
    data: {
      storeName: modal.value.keyword,
    },
    pageVO: {
      current: pageVO.current,
      size: pageVO.size,
    },
  };

  return _searchParams;
}
/** 数据初始化 */
async function initPckUpLocationList() {
  isPageLoadingRef.value = true;
  ininParams();
  await getPckUpLocationList();
  isPageLoadingRef.value = false;
}
/** 获取会员门店附近自提点 */
async function getPckUpLocationList() {
  const { current, size } = pageVO;
  try {
    isPageLoadingRef.value = current === 1;
    const _params = getSearchParams();
    const { records = [], total = 0 } = await getNearbyStoreBasicInfoPage(_params);
    // 更新订单列表
    if (current === 1) {
      pckUpLocationList.value = records;
    } else if (records.length) {
      pckUpLocationList.value.push(...records);
    }
    // 更新分页状态
    const hasMore = current * size < total;
    Object.assign(pageVO, {
      current: current,
      total: Number(total),
    });
    isFinishedRef.value = !hasMore;
  } catch (error) {
    if(error === "获取地理位置失败，请稍后！") {
        isLoadingRef.value =true
        onRefresh();
    } else {
        createMessageError(error);
    }
    console.log('error', error);

  } finally {
    isLoadingRef.value = false;
    refreshingRef.value = false;
    isPageLoadingRef.value = false;
  }
}

/**
 * 搜索处理
 */
const handleSearch = () => {
  initPckUpLocationList();
};
const handleSave = () => {
  const find = pckUpLocationList.value.find(item => item.shortId === selected.value);
  find && (_orderInfo.value.storeEntity = find);
  router.replace({
    name: RoutesName.StoreConfirmOrder,
    query: {
      orderInfo: encodeURIComponent(JSON.stringify(_orderInfo.value)),
      type: props.type,
    },
  });
};
onMounted(() => {
  if (props.orderInfo) {
    const orderInfo = JSON.parse(decodeURIComponent(props.orderInfo)) || {};
    _orderInfo.value = orderInfo;
    selected.value = orderInfo?.storeEntity?.shortId;
  }
  initPckUpLocationList();
});
</script>

<style scoped lang="less">
@import url("@/styles/storeVar.less");

.pck-up-location-page {
  height: 100%;
  overflow: hidden;
  .j-loading-wrapper {
    :deep(.j-loading-wrapper-content) {
      overflow: hidden;
    }
  }
  .content-wrapper {
    width: calc(100% - 12px * 2);
    height: calc(100% - 52px - 68px - 12px);
    overflow-y: scroll;
    margin-top: 12px;
    padding: 0 12px;
    .card-item {
      display: flex;
      width: 100%;
      height: 90px;
      background: #ffffff;
      border-radius: 8px;
      padding: 12px;
      box-sizing: border-box;
      margin-bottom: 12px;
      align-items: center;
      .content-img {
        width: 66px;
        height: 66px;
        border-radius: 4px;
        margin-right: 8px;
      }

      .content-info {
        display: flex;
        flex-direction: column;
        justify-content: center;
        .info-title {
          font-size: 16px;
          font-weight: 500;
          color: #333333;
        }

        .info-footer {
          .footer-info {
            display: flex;
            align-items: center;
            display: flex;
            align-items: center;
            margin-top: 6px;
            img {
              width: 16px;
              height: 16px;
              margin-right: 4px;
            }

            .desc {
              display: flex;
              font-size: 12px;

              .desc-left {
                margin-right: 5px;
              }
            }
          }
        }
      }
    }
    .active {
      position: relative;
      border-radius: 8px 8px 8px 8px;
      border: 1px solid #ef1115;
      .selected {
        position: absolute;
        bottom: 0;
        right: -1px;
      }
    }
  }
  .footer-box {
    position: fixed;
    bottom: env(safe-area-inset-bottom);
    left: 0;
    width: 100vw;
    padding: 12px 8px;
    box-sizing: border-box;
    background: #fff;
    .btn {
      width: 100%;
      background-color: @error-color;
      color: #fff;
    }
  }
}
</style>
