<template>
  <JLoadingWrapper :show="isPageLoadingRef">
    <div class="store_manager_leaderboard_wrapper">
      <!-- header -->
      <div class="header">
        <div class="header_img">
          <img :src="storeRanking" alt="" style="width: 160px; height: 38px;" />
          <img :src="storeRanking2" alt="" style="width: 120px; height: 18px;" />
        </div>
        <!-- 时间筛选 -->
        <div class="header_time">
          <div
            v-for="item in storeRankingTimeOption"
            :key="item.value"
            class="time-item"
            :class="{ active: activeTime === item.value }"
            @click="handleTimeChange(item.value)"
          >
            {{ item.label }}
          </div>
        </div>
      </div>
      <!-- 内容 -->
      <div class="content">
        <!-- 搜索框 -->
        <div class="search-box">
          <VanSearch
            @search="handleSearch"
            @clear="handleSearch"
            @click-left-icon="handleSearch"
            shape="round"
            v-model="modal.keyword"
            placeholder="请输入店长昵称"
          />
        </div>
        <!-- 表头 -->
        <div class="table-header">
          <div class="table-header-item">排名</div>
          <div class="table-header-item" style="flex: 2;">店长</div>
          <div class="table-header-item">销售额</div>
        </div>

        <!-- 表格 -->
        <div class="table-content">
          <VanPullRefresh v-model="refreshingRef" @refresh="onRefresh">
            <VanList
              v-if="storeManagerLeaderboardList.length"
              v-model:loading="loadingRef"
              :finished="isFinishedRef"
              finished-text="没有更多了"
              @load="onLoad"
              :immediate-check="false"
            >
              <ManagerLeaderboardCard
                v-for="item,index in storeManagerLeaderboardList"
                :rank="index + 1"
                :key="`${item.memberId}${index}`"
                :rankingInfo="item"
              />
            </VanList>
            <!-- 无数据时显示空状态 -->
            <EmptyData v-else />
          </VanPullRefresh>
        </div>
      </div>
    </div>
  </JLoadingWrapper>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from "vue";
import dayjs from "dayjs";
import { usePaginatedFetch } from "@/views/StoreModule/hooks";
import { leaderboardByStore } from "@/services/storeApi";
/** 资源 */
import storeRanking from "@/assets/storeImage/storeMine/store-ranking.png";
import storeRanking2 from "@/assets/storeImage/storeMine/store-ranking2.png";
/** 相关组件 */
import EmptyData from "@/views/StoreModule/components/EmptyData.vue";
import ManagerLeaderboardCard from "./components/ManagerLeaderboardCard.vue";
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";

defineOptions({ name: "StoreManagerLeaderboard" });

const enum RankType {
  Month = 1,
  LastMonth = 2,
  Total = 3,
}

const activeTime = ref(RankType.Month);

const storeRankingTimeOption = ref([
  { label: "本月", value: RankType.Month },
  { label: "上月", value: RankType.LastMonth },
  { label: "总榜", value: RankType.Total },
]);

/** 初始化查询参数 */
const initParams = {
  keyword: null,
};
const modal = ref({ ...initParams });

/**
 * 根据时间类型获取时间范围
 */
function getTimeRangeByType(timeType: RankType): { startTime: string; endTime: string } {
  let startDate: dayjs.Dayjs;
  let endDate: dayjs.Dayjs;

  switch (timeType) {
    case RankType.Month:
      // 本月：从本月第一天到当前时间
      startDate = dayjs().startOf("month");
      endDate = dayjs().endOf("day");
      break;
    case RankType.LastMonth:
      // 上月：从上月第一天到上月最后一天
      startDate = dayjs().subtract(1, "month").startOf("month");
      endDate = dayjs().subtract(1, "month").endOf("month");
      break;
    case RankType.Total:
      startDate = null;
      endDate = null;
      break;
    default:
      // 默认使用本月
      startDate = dayjs().startOf("month");
      endDate = dayjs().endOf("day");
  }

  return {
    startTime: startDate ? startDate.format("YYYY-MM-DD HH:mm:ss") : null,
    endTime: endDate ? endDate.format("YYYY-MM-DD HH:mm:ss") : null,
  };
}

/** 获取搜索参数 */
function getSearchParams() {
  const { keyword } = modal.value;

  // 根据当前选中的时间类型获取时间范围
  const { startTime, endTime } = getTimeRangeByType(activeTime.value);

  return {
    storeManagerName: keyword,
    startTime,
    endTime,
  };
}

/** 分页数据加载Hook */
const {
  isPageLoadingRef,
  setPageLoadingTrue,
  setPageLoadingFalse,
  refreshingRef,
  loadingRef,
  isFinishedRef,
  searchParamsRef,
  pageListRef: storeManagerLeaderboardList,
  initPageDataRequest: initStoreManagerLeaderboardList,
  onLoad,
  onRefresh,
} = usePaginatedFetch({
  fetchApi: leaderboardByStore,
  searchParams: {}, // 初始化时设置搜索参数
  beforeRequest: (params) => {
    return new Promise((resolve) => {
      resolve({
        ...params,
        data: {
          ...params.data,
          ...getSearchParams(),
        }
      });
    });
  }
});

/** 时间切换处理 */
const handleTimeChange = async (value: number) => {
  activeTime.value = value;
  // 时间切换时重新加载数据
  setPageLoadingTrue();
  await initStoreManagerLeaderboardList();
  setPageLoadingFalse();
};

/** 搜索处理 */
function handleSearch() {
  // 搜索时重新加载数据
  initStoreManagerLeaderboardList();
}

// 初始化加载数据
onMounted(async () => {
  setPageLoadingTrue();
  await initStoreManagerLeaderboardList();
  setPageLoadingFalse();
});
</script>

<style lang="less" scoped>
.store_manager_leaderboard_wrapper {
  width: 100vw;
  height: calc(100vh - env(safe-area-inset-bottom));
  background: url("@/assets/storeImage/storeMine/store-ranking-bg.png") no-repeat;
  background-size: 100% 260px;
  padding: 12px 12px 0 12px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;

  .header {
    width: 100%;
    height: 165px;
    box-sizing: border-box;

    .header_img {
      display: flex;
      flex-direction: column;
      gap: 4px;
      margin: 25px 0 29px 0;
    }

    .header_time {
      width: 220px;
      height: 36px;
      background: rgba(255, 255, 255, 0.6);
      border-radius: 21px;
      display: flex;
      align-items: center;

      .time-item {
        flex: 1;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 16px;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #FFAD89;
        text-align: left;
        font-style: normal;
        text-transform: none;
        cursor: pointer;
        transition: all 0.3s ease;

        &.active {
          background: #fff;
          color: #FF7542;
          font-weight: 500;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
      }
    }
  }

  .content {
    width: 100%;
    flex: 1;
    background: #FFFFFF;
    border-radius: 12px;
    padding: 0 12px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    overflow: hidden; // 添加这行

    .search-box {
      height: 62px;

      .van-search {
        padding: 16px 0 12px 0;

        :deep(.van-cell) {
          background-color: transparent !important;
          padding: 0 8px 0 0 !important;
        }
      }
    }

    .table-header {
      height: 40px;
      display: flex;
      align-items: center;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 500;
      font-size: 14px;
      color: #666666;
      line-height: 18px;
      text-align: left;
      font-style: normal;
      text-transform: none;

      .table-header-item {
        flex: 1;
      }
    }

    .table-content {
      flex: 1;
      overflow: hidden; // 重要：确保容器不会无限扩展

      // VanPullRefresh 和 VanList 样式调整
      :deep(.van-pull-refresh) {
        height: 100%;
      }

      :deep(.van-list) {
        height: 100%;
        overflow-y: auto;
        &::-webkit-scrollbar {
          display: none;
        }

        -ms-overflow-style: none;
        scrollbar-width: none;
      }
    }
  }
}
</style>
