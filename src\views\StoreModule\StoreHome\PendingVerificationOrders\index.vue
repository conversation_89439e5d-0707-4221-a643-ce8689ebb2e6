<template>
  <JLoadingWrapper :show="isPageLoadingRef" style="height: 100vh;">
    <!-- 门店店长、店员操作 -->
    <div class="store_select_search_flex">
      <StoreSelectSearch
        v-model:value="searchValueRef"
        v-model:searchTypeValue="searchTypeRef"
        :searchTypeOptions="searchTypeOptions"
        @search="initPendingOrdersList"
        style="flex: 1;"
      />
      <!-- 批量 -->
      <div class="store_select_search_flex_right">
        <span v-if="!isShowWriteOffOrder" @click="handleBatchVerify" class="store_select_search_flex_right_span">
          批量
        </span>
        <!-- 取消 -->
        <span v-else class="store_select_search_flex_right_span" @click="handleBatchCancel">取消</span>
      </div>
    </div>
    <!-- 主体 -->
    <div class="pending-orders-content-h2">
      <VanPullRefresh v-model="refreshingRef" @refresh="onRefresh" @scroll="onScroll" class="pending-orders-content">
        <template v-if="pendingVerificationOrders.length">
          <VanList v-model:loading="isLoadingRef" :finished="isFinishedRef" finished-text="没有更多了" @load="onLoad">
            <PendingOrdersCard
              v-for="item in pendingVerificationOrders"
              :key="item"
              :orderInfo="item"
              @writeOffOrder="handleWriteOffOrder"
              @click="handleToOrderDetail(item)"
              @change="handleOrderSelect"
              :show-checkbox="isShowWriteOffOrder"
              :checked="selectedOrders[item.id]?.checked || false"
            />
          </VanList>
        </template>
        <template v-else>
          <EmptyData />
        </template>
      </VanPullRefresh>
    </div>
    <!-- footer -->
    <div v-if="isShowWriteOffOrder" class="footer">
      <!-- 全选 -->
      <VanCheckbox 
        v-model="isAllSelectedInFooter"
        checked-color="#EF1115"
        style="font-size: 18px;"
        :disabled="selectableCount === 0"
        @click.stop=""
      >
        <span class="footer__label">
          全选
          <span class="footer__label_title">(一次最多核销100张订单)</span>
        </span>
      </VanCheckbox>
      <VanButton type="danger" round @click.stop="handleBatchWriteOff" size="small" style="width: 120px;height: 40px;">
        一键核销
      </VanButton>
    </div>
    <!-- 核销订单 -->
    <WriteOffOrder
      v-model:show="isShowWriteOffOrderRef"
      @confirm="handleConfirmWriteOffOrder"
      :isBatch="isShowWriteOffOrder"
      :batchCount="currentSelectedOrders.length"
    />
  </JLoadingWrapper>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onActivated, nextTick, computed } from "vue";
import { showToast } from "vant";
import useGetPendingOrders from "./hooks/useGetPendingOrders";
import { useMessages } from "@/hooks/useMessage";
import { useBoolean } from "@/views/StoreModule/hooks";
import { StorePendingOrderRouteTypeEnum, KeepAliveRouteNameEnum, StoreOrderDetailRouteTypeEnum } from "@/views/StoreModule/enums";
import { useRouterUtils } from "@/views/StoreModule/hooks";
import { useKeepAliveRoute } from '@/hooks/useKeepAliveRoute';
import { RoutesName } from "@/enums/routes";
import { SearchTypeEnum } from "./type";
import { verifyOrder, batchVerify } from "@/services/storeApi";
/**  相关组件 */
import StoreSelectSearch from "@/views/StoreModule/components/StoreSelectSearch.vue";
import EmptyData from "@/views/StoreModule/components/EmptyData.vue";
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";
import PendingOrdersCard from "./components/PendingOrdersCard.vue";
import WriteOffOrder from "@/views/StoreModule/components/WriteOffOrder.vue";

defineOptions({ name: KeepAliveRouteNameEnum.PENDING_ORDER });

/** props */
const props = defineProps<{
  userId: string;
  type: StorePendingOrderRouteTypeEnum;
}>();

const { pushKeepAliveRoute, deleteKeepAliveRouteByName, scrollEventHandler, restoreScrollPositionByDom } = useKeepAliveRoute();
const {
  isPageLoadingRef,
  refreshingRef,
  isLoadingRef,
  isFinishedRef,
  pendingVerificationOrders,
  searchValueRef,
  searchTypeRef,
  onRefresh,
  onLoad,
  getPendingOrders,
  initPendingOrdersList
} = useGetPendingOrders({
  userId: props.userId,
  type: props.type
});
const { routerPushByRouteName } = useRouterUtils();
const { createMessageError, createMessageSuccess } = useMessages();
const isShowWriteOffOrderRef = ref(false);
/** 当前核销订单 */
const currentVerifyOrderInfoRef = ref({
  code: null,
  verifyCode: null
});

/** 搜索类型 */
const searchTypeOptions = computed(() => {
  // 根据页面类型决定是否显示客户昵称
  // MY_WAIT_VERIFY 显示客户昵称，SCAN_WAIT_VERIFY 不显示
  const showCustomerName = props.type == StorePendingOrderRouteTypeEnum.MY_WAIT_VERIFY;
  
  if (showCustomerName) {
    return [
      { text: "客户昵称", value: SearchTypeEnum.CustomerName },
      { text: "商品名称", value: SearchTypeEnum.ProductName },
      { text: "订单编号", value: SearchTypeEnum.OrderNo },
    ];
  } else {
    return [
      { text: "商品名称", value: SearchTypeEnum.ProductName },
      { text: "订单编号", value: SearchTypeEnum.OrderNo },
    ];
  }
});

// 计算内容区域高度
const contentHeight = computed(() => {
  const baseHeight = '100vh'; // 基础高度

  if (props.type === StorePendingOrderRouteTypeEnum.SCAN_WAIT_VERIFY) {
    // SCAN_WAIT_VERIFY: 100vh - 底部一键核销区域高度
    return isShowWriteOffOrder.value
      ? 'calc(100vh - 50px - env(safe-area-inset-bottom))'
      : '100vh';
  } else {
    // MY_WAIT_VERIFY: 100vh - 顶部搜索区域高度 - 底部一键核销区域高度
    const searchHeight = '48px'; // 搜索区域高度
    return isShowWriteOffOrder.value
      ? `calc(100vh - ${searchHeight} - 50px - env(safe-area-inset-bottom))`
      : `calc(100vh - ${searchHeight})`;
  }
});

/** 跳转订单详情 */
function handleToOrderDetail(orderInfo) {
  if (isShowWriteOffOrder.value) {
    return;
  }
  pushKeepAliveRoute(KeepAliveRouteNameEnum.PENDING_ORDER);
  routerPushByRouteName(RoutesName.StoreOrderDetail, {
    routeType: StoreOrderDetailRouteTypeEnum.SCAN,
    orderCode: orderInfo?.code ?? "",
  });
}

/** 滚动触发 */
function onScroll(e) {
  scrollEventHandler(e, KeepAliveRouteNameEnum.PENDING_ORDER);
}

function handleWriteOffOrder(orderInfo) {
  isShowWriteOffOrderRef.value = true;
  currentVerifyOrderInfoRef.value = {
    code: orderInfo?.code ?? "",
    verifyCode: orderInfo?.orderVerificationDTO?.code ?? "",
  };
}

/** 核销订单 */
async function handleConfirmWriteOffOrder(isBatch = false) {
  if (isBatch) {
    await handleBatchWriteOffOrder();
  } else {
    try {
      const { code, verifyCode } = currentVerifyOrderInfoRef.value;
      const _params = {
        code,
        verifyCode
      };
      const resp = await verifyOrder(_params);
      if (resp) {
        createMessageSuccess("核销成功");
        currentVerifyOrderInfoRef.value = {
          code: null,
          verifyCode: null
        };
        onRefresh();
      }
    } catch (error) {
      createMessageError("核销失败：" + error);
      currentVerifyOrderInfoRef.value = {
        code: null,
        verifyCode: null
      };
    }
  }
}

/** 已选订单数量 */
const selectedCount = computed(() => {
  return Object.values(selectedOrders).filter(item => item.checked).length;
});

/** 可选订单数量 */
const selectableCount = computed(() => {
  return pendingVerificationOrders.value.length;
});

/** 是否全部选中 */
const isAllSelected = computed(() => {
  return selectedCount.value === selectableCount.value && selectableCount.value > 0;
});

/** 底部全选状态（用于底部复选框）*/
const isAllSelectedInFooter = computed({
  get: () => isAllSelected.value,
  set: (value) => {
    if (value) {
      selectAll();
    } else {
      unselectAll();
    }
  }
});

/** 当前已选择的订单 */
const currentSelectedOrders = computed(() => {
  return Object.values(selectedOrders).filter(item => item.checked);
});

/** 全选功能 */
function selectAll() {
  pendingVerificationOrders.value.forEach(order => {
    const orderId = order.id;
    if (!selectedOrders[orderId]) {
      selectedOrders[orderId] = {
        checked: true,
        code: order.code,
        verifyCode: order?.orderVerificationDTO?.code
      };
    } else {
      selectedOrders[orderId].checked = true;
    }
  });
}

/** 全不选功能 */
function unselectAll() {
  pendingVerificationOrders.value.forEach(order => {
    const orderId = order.id;
    if (selectedOrders[orderId]) {
      selectedOrders[orderId].checked = false;
    }
  });
}

/** 选中的订单状态存储 */
const selectedOrders = reactive<Record<string, {
  checked: boolean;
  code: string;
  verifyCode: string;
}>>({});

/** 一键核销 */
const { bool: isShowWriteOffOrder, setTrue: showWriteOffOrder, setFalse: hideWriteOffOrder } = useBoolean();

/** 点击批量 */
function handleBatchVerify() {
  showWriteOffOrder();
  initSelectedOrders();
}

/** 点击取消 */
function handleBatchCancel() {
  hideWriteOffOrder();
}

/** 批量核销 */
async function handleBatchWriteOffOrder() {
  try {
    const selectedItems = Object.values(selectedOrders).filter(item => item.checked);
    
    // 检查选中的订单数量
    if (selectedItems.length === 0) {
      createMessageError("请先选择要核销的订单");
      return;
    }
    
    // 检查是否超过最大限制
    if (selectedItems.length > 100) {
      createMessageError(`一次性最多核销100张订单，当前选择了${selectedItems.length}张`);
      return;
    }
    
    const _params = selectedItems.map(item => {
      return {
        code: item.code,
        verifyCode: item.verifyCode
      };
    });
    
    const resp = await batchVerify(_params);
    if (resp) {
      if (resp > 0) {
        createMessageSuccess(`成功核销${resp}张订单`);
        clearSelectedOrders();
        onRefresh();
      } else {
        createMessageError("未成功核销订单");
      }
    }
  } catch (error) {
    createMessageError("核销失败：" + error);
  }
}

/** 清空选中的订单状态 */
function clearSelectedOrders() {
  Object.keys(selectedOrders).forEach(key => {
    delete selectedOrders[key];
  });
}

/** 选择 */
function handleOrderSelect(checked: boolean, orderInfo: {
  id: string;
  code: string;
  orderVerificationDTO: {
    code: string;
  };
}) {
  const orderId = orderInfo.id;
  console.log("orderId", orderId);
  console.log("selectedOrders", selectedOrders);
  
  
  if (!selectedOrders[orderId]) {
    selectedOrders[orderId] = {
      checked,
      code: orderInfo.code,
      verifyCode: orderInfo?.orderVerificationDTO?.code,
    };
  } else {
    // 只修改 checked 状态，不要删除整个记录
    selectedOrders[orderId].checked = checked;
  }
}

/** 批量核销 */
function handleBatchWriteOff() {
  // 这里实现批量核销逻辑
  console.log('执行批量核销');
  if (currentSelectedOrders.value.length === 0) {
    showToast("请先选择需要核销的订单");
    return;
  }
  isShowWriteOffOrderRef.value = true;
}

/** 初始化选中数据（可选） */
function initSelectedOrders() {
  pendingVerificationOrders.value.forEach(order => {
    const orderId = order.id;
    if (!selectedOrders[orderId]) {
      selectedOrders[orderId] = {
        checked: false,
        code: order.code,
        verifyCode: order?.orderVerificationDTO?.code,
      };
    }
  });
}

/** 组件挂载 */
onMounted(() => {
  getPendingOrders();
});

onActivated(() => {
  nextTick(() => {
    const el = document.getElementsByClassName("pending-orders-content")[0];
    restoreScrollPositionByDom(el, KeepAliveRouteNameEnum.PENDING_ORDER);
    deleteKeepAliveRouteByName(KeepAliveRouteNameEnum.PENDING_ORDER);
  });
});
</script>

<style lang="less" scoped>
.store_select_search_flex {
  width: 100%;
  min-height: 48px;
  display: flex;
  align-items: center;
  background-color: #fff;
  .store_select_search_flex_right {
    padding-right: 12px;
    margin-left: auto;
    .store_select_search_flex_right_span {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      line-height: 20px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }
}

// 动态高度容器
.pending-orders-content-container {
  width: 100%;
  transition: height 0.3s ease;
}

.pending-orders-content {
  width: 100%;
  height: 100%;
  padding: 12px 10px;
  box-sizing: border-box;
  overflow-y: auto;

  /* 隐藏滚动条但保持滚动功能 */
  &::-webkit-scrollbar {
    display: none;
  }
  -ms-overflow-style: none;
  scrollbar-width: none;
}

// SCAN_WAIT_VERIFY 类型的高度
.pending-orders-content-h1 {
  transition: height 0.3s ease;

  // 无底部一键核销时
  &:not(:has(~ .footer)) {
    height: 100vh;
  }

  // 有底部一键核销时
  &:has(~ .footer) {
    height: calc(100vh - 50px - env(safe-area-inset-bottom));
  }
}

// MY_WAIT_VERIFY 类型的高度
.pending-orders-content-h2 {
  transition: height 0.3s ease;

  // 无底部一键核销时
  &:not(:has(~ .footer)) {
    height: calc(100vh - 48px);
  }

  // 有底部一键核销时
  &:has(~ .footer) {
    height: calc(100vh - 48px - 50px - env(safe-area-inset-bottom));
  }
}

// 或者使用更简洁的计算方式（推荐）
.pending-orders-content-h1 {
  height: v-bind(contentHeight);
}

.pending-orders-content-h2 {
  height: v-bind(contentHeight);
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: calc(50px + env(safe-area-inset-bottom));
  background: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0px 12px;
  box-sizing: border-box;
  padding-bottom: env(safe-area-inset-bottom);
  border-top: 1px solid #f0f0f0;
  z-index: 1000;

  .footer__label {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 18px;
    color: #333333;
    line-height: 18px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    .footer__label_title {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #999999;
      line-height: 20px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }

  :deep(.van-button__text) {
    font-family: Source Han Sans CN;
    font-weight: 500;
    font-size: 14px;
    line-height: 24px;
  }
}
</style>
