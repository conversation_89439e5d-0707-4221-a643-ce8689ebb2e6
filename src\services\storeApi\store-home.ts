import { defHttp } from "@/services";
import { getStoreApiUrl } from "@/utils/http/urlUtils";

/** 首页 */
export const enum storeHomeApiEnum {
  // 获取门店基础信息
  getBasicInfo = "/h5/storeEntity/getBasicInfo",
  // 门店轮播图列表
  listImg = "/h5/carouselImg/listImg",
  // 门店最新商品列表
  listNewProduct = "/applet/product/manage/storesProductPage",
  // 通过useId,查询该用户福利券领取记录
  getWelfareCouponByUserId = "/h5/couponReceiveRecord/getByCouponUseId",
  // 核销优惠券
  verifyCoupon = "/h5/couponReceiveRecord/redeemCoupon",
  // 获取二维码用户ID
  getQrCodeCsId = "/H5/QrCode/getQrCodeCsId",
  // 通过券分类Id,查询券明细
  getByCouponId = "/h5/couponReceiveRecord/getByCouponId",
  // 获取用户今日最近进入的直播间链接
  getLiveRoomUrl = "/h5/live/getLastLiveUrl",
  // 通过券分类Id,userId,查询券明细（扫码后查看券明细）
  getByCouponIdAndUserId = "/h5/couponReceiveRecord/getByCouponIdAndUserId",
  // 获取邀请填写收货地址的状态
  getInviteAddressStatus = "/h5/customerAddress/getShippingAddressStatus",
  // 获取用户观看直播以天为维度的数据
  getLiveRoomData = "/shareRecord/getDailyWatchByMember",
  // 查看用户某天观看直播进出记录
  getLiveRoomRecord = "/shareRecord/getDailyEnterExitDetail"
}

/**
 * @description 获取用户观看直播以天为维度的数据
 */
export function getLiveRoomData(_params = {}) {
  return defHttp.get({
    url: getStoreApiUrl(storeHomeApiEnum.getLiveRoomData),
    params: _params,
    requestConfig: {
      skipCrypto: true,
      isQueryParams: true,
    },
  });
}

/**
 * @description 查看用户某天观看直播进出记录
 */
export function getLiveRoomRecord(_params = {}) {
  return defHttp.get({
    url: getStoreApiUrl(storeHomeApiEnum.getLiveRoomRecord),
    params: _params,
    requestConfig: {
      skipCrypto: true,
      isQueryParams: true,
    },
  });
}

/** 获取邀请填写收货地址的状态 */
export function getInviteAddressStatus(_params = {}) {
  return defHttp.post({
    url: getStoreApiUrl(storeHomeApiEnum.getInviteAddressStatus),
    params: _params,
    requestConfig: {
      skipCrypto: true,
    },
  });
}

/** 获取门店基础信息 */
export function getStoreBasicInfo(_params:{ id: string }) {
  return defHttp.get({
    url: getStoreApiUrl(storeHomeApiEnum.getBasicInfo),
    params: _params,
    requestConfig: {
      isQueryParams: true,
      skipCrypto: true,
    },
  });
}

/** 门店轮播图列表 */
export function getStoreCarouselImage(_params:{ CurrentPosition?: number }) {
  return defHttp.get({
    url: getStoreApiUrl(storeHomeApiEnum.listImg),
    params: _params,
    requestConfig: {
      isQueryParams: true,
      skipCrypto: true,
    },  
  });
}

/** 门店最新商品列表 */
export function getStoreNewProductList(_params) {
  return defHttp.post({
    url: getStoreApiUrl(storeHomeApiEnum.listNewProduct),
    params: _params,
    requestConfig: {
      skipCrypto: true,
    }
  });
}

/** 通过useId,查询该用户福利券领取记录 */
export function getWelfareCouponByUserId(_params) {
  return defHttp.get({
    url: getStoreApiUrl(storeHomeApiEnum.getWelfareCouponByUserId),
    params: _params,
    requestConfig: {
      skipCrypto: true,
      isQueryParams: true,
    },
  });
}

/**
 * @description 查询福利券明细
 */
export function getWelfareCouponDetail(_params = {}) {
  return defHttp.get({
    url: getStoreApiUrl(storeHomeApiEnum.getByCouponId),
    params: _params,
    requestConfig: {
      skipCrypto: true,
      isQueryParams: true,
    },
  });
}

/**
 * @description 通过券分类Id,userId,查询券明细（扫码后查看券明细）
 */
export function getWelfareCouponByUserIdAndCouponId(_params = {}) {
  return defHttp.get({
    url: getStoreApiUrl(storeHomeApiEnum.getByCouponIdAndUserId),
    params: _params,
    requestConfig: {
      skipCrypto: true,
      isQueryParams: true,
    },
  });
}

/** 核销优惠券 */
export function verifyCoupon(_params:{ 
  couponId?: string;
  userId: string;
  couponBatchId?: string;
  redeemNum?: number;
}) {
  return defHttp.post({
    url: getStoreApiUrl(storeHomeApiEnum.verifyCoupon),
    params: { data: _params },
    requestConfig: {
      skipCrypto: true,
    },
  });
}

/** 获取二维码用户ID */
export function getQrCodeUserId(_params = {}) {
  return defHttp.get({
    url: getStoreApiUrl(storeHomeApiEnum.getQrCodeCsId),
    params: _params,
    requestConfig: {
      isQueryParams: true,
      skipCrypto: true,
    },
  });
};

/**
 * @description 获取用户今日最近进入的直播间链接
 */
export function getLiveRoomLink(_params = {}) {
  return defHttp.get({
    url: getStoreApiUrl(storeHomeApiEnum.getLiveRoomUrl),
    params: _params,
    requestConfig: {
      isQueryParams: true,
      skipCrypto: true,
    }, 
  });
}