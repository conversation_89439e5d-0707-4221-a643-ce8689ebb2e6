import { defHttp } from "@/services";
import { getStoreApiUrl } from "@/utils/http/urlUtils";
const enum SystemApiEnum {
  getAgreementByKey = "/applet/globalConfigs/getAgreement",
  getStoreLogo = '/h5/homeLogo/getLogo',
  acceptStoreBelong = '/h5/store/personal/acceptStoreBelong',
  // 获取是否允许切换自提点
  getPickUpStore = '/h5/globalConfigs/getPickUpStore',
  // 获取自提商品确认订单时是否填写信息确认
  getNeedCustomerInfo = '/h5/globalConfigs/getNeedCustomerInfo',
  // 获取分佣提现时间
  getWalletWithdrawTime = '/h5/globalConfigs/getWalletWithdrawTime'
}
// 查询协议
export async function getAgreementByKey(params = {}) {
  return defHttp.get({
    url: getStoreApiUrl(SystemApiEnum.getAgreementByKey),
    params,
    requestConfig: {
      withToken: true,
    },
  });
}


interface StoreInfoResponse{
  createTime: string,
  homeImgPath: string,
  id: string,
  imgPath: string,
  isEnableImg: number,
  isEnableName: number,
  name: string,
  slogan: string,
  updateTime: string,
}
export async function getStoreLogo() {
  return defHttp.get<StoreInfoResponse>({
    url: getStoreApiUrl(SystemApiEnum.getStoreLogo),
    requestConfig: {
      withToken: true,
    },
  });
}
export async function acceptStoreBelong(state:string) {
  return defHttp.get({
    url: getStoreApiUrl(SystemApiEnum.acceptStoreBelong),
    params:{
      state
    }
  });
}
// 获取是否允许切换自提点
export async function getPickUpStore() {
  return defHttp.get({
    url: getStoreApiUrl(SystemApiEnum.getPickUpStore),
  });
}
// 获取自提商品确认订单时是否填写信息确认
export async function getNeedCustomerInfo() {
  return defHttp.get({
    url: getStoreApiUrl(SystemApiEnum.getNeedCustomerInfo),
  });
}
// 获取自提商品确认订单时是否填写信息确认
export async function getWalletWithdrawTime() {
  return defHttp.get({
    url: getStoreApiUrl(SystemApiEnum.getWalletWithdrawTime),
  });
}