import { defHttp } from "@/services";
import { getStoreApiUrl } from "@/utils/http/urlUtils";
const enum LiveViewApi{
    hsLiveLink = '/h5/live/liveView',
    listWatchTimeCoupon = '/h5/live/listWatchTimeCoupon',
    receiveCoupon = '/h5/live/receiveCoupon',
    // 获取分享券
    getShareCoupon = '/h5/live/getShareCoupon',
    // 领取分享券
    receiveShareCoupon = '/h5/live/receiveShareCoupon',
    // 点击浮窗商品
    clickFloatingCard = '/h5/live/clickedFloatingProduct',
}

export interface HsLiveLinkResponse{
    status: number  
    hsLiveLink: string,
    liveRoomId: number | string,
    billboardEnable: number,
    billboardImageUrl: string,
    billboardLinkUrl: string,
    newBelong:number,
    token?:string
}

export interface ListWatchTimeCouponResponse{
    /* 已观看秒数,从火山获取，有几分钟延迟 */
    watchedSeconds: number,
    watchTimeCouponDTOList: Array<LiveCouponInfoResponse>
}

/* 直播间时长列表 */
export interface LiveCouponInfoResponse {
    // 优惠券批次 ID
    couponBatchId: number;
    // 直播间 ID
    liveRoomId: number;
    // 分类名称
    categoryName: string;
    // 福利券分类-id
    categoryId: number | string
    // 图片 URL
    imageUrl: string;
    // 所需观看秒数
    requiredSeconds: number;
    // 领取标志
    receiveFlag: number;
}

/* 领取时长券结果 */
export interface receiveCouponVo {
    /**
     * 福利券分类-id
     */
    categoryId: number | string;
    /**
     * coupon_batch_id_ 福利券ID
     * 福利券批次ID
     */
    couponBatchId: number;
    /**
     * live_room_id_ 直播间ID
     * 直播间ID
     */
    liveRoomId: number | string;
}




export function getHsLiveLink(state,shareUserId){
    return defHttp.post<HsLiveLinkResponse>({
        url: getStoreApiUrl(`${LiveViewApi.hsLiveLink}?state=${state}&shareUserId=${shareUserId}`),
        params:{}
    });
}

/**
 * 获取直播观看时间券列表
 * @param activityId 直播间id
 */
export function getLiveWatchTimeCouponList(activityId){
    return defHttp.get<ListWatchTimeCouponResponse>({
        url: getStoreApiUrl(`${LiveViewApi.listWatchTimeCoupon}?activityId=${activityId}`),
        params:{}
    });
}

/**
 * 领取直播观看时间券
 * @param params
 */
export function receiveCoupon(params:receiveCouponVo){
    return defHttp.post({
        url: getStoreApiUrl(`${LiveViewApi.receiveCoupon}`),
        params
    });
}

/**
 * 获取分享券
 * @param activityId 直播间id
 */
export function getShareCoupon(activityId){
    return defHttp.get({
        url: getStoreApiUrl(`${LiveViewApi.getShareCoupon}?activityId=${activityId}`),
        params:{}
    });
}

/**
 * 领取分享券
 * @param params
 */
export function receiveShareCoupon(params){
    return defHttp.post({
        url: getStoreApiUrl(`${LiveViewApi.receiveShareCoupon}`),
        params
    });
}

/**
 * 点击浮窗商品
 * @param params
 */
export function clickFloatingCard(params){
    return defHttp.post({
        url: getStoreApiUrl(`${LiveViewApi.clickFloatingCard}`),
        params
    });
}