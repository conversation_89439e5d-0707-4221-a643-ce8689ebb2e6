<template>
    <div class="login-wrapper">
        <p class="title">会员登录</p>
        <!-- <img class='store-logo' :src="storeInfo.logoUrl" alt="">
        <p class="store-name">{{ storeInfo.name }}</p> -->
        <p class="store-name">LPD XJ</p>
        <van-button color='#EF1115' round block @click="login" :disabled="isLoginDisabledComputed">一键登录</van-button>
        <van-popover 
            :show="isShowPopupRef"
            :close-on-click-action="false"
            :close-on-click-outside="false"
            :close-on-click-overlay="false"
            placement="bottom-start"
        >
            <p style="padding: 8px;background:#1677FF;color: #fff;font-size: 12px;">请阅读协议并勾选同意</p>
            <template #reference>
             <van-checkbox 
                  class='checkbox-wrapper' 
                  checked-color='#EF1115' 
                  icon-size='14px' 
                  v-model="formRef.isChecked"
              >
                  我已阅读并同意<span class="primary-text">《用户协议》</span>与<span class="primary-text">《隐私政策》</span>
              </van-checkbox>
            </template>
        </van-popover>
        
    </div>
</template>
<script setup lang="ts">
import { useMessages } from '@/hooks/useMessage';
import { getStoreLoginWxappID } from '@/services/storeApi';
import { getStoreLogo } from '@/services/storeApi';
import { CacheConfig } from '@/utils/cache/config';
import { createCacheStorage } from '@/utils/cache/storageCache';
import { parseUrlParams } from '@/utils/http/urlUtils';
import { QWAuthTypeEnum, redirectToWebpageAuthorization, type WXAuthConfig } from '@/utils/wxUtils';
import { computed, onMounted, reactive, ref, watch } from 'vue';
const {createMessageError} = useMessages()

    const formRef = reactive({
        isChecked: true
    })
    const storeInfo = reactive({
        appId:'',
        name:'',
        logoUrl:''
    })

    const isLoginDisabledComputed = computed(() => {
        return !formRef.isChecked
    })

    const isShowPopupRef = ref(false)
    function login(){
        if(formRef.isChecked == false){
            createMessageError('请先阅读并勾选同意协议')
            isShowPopupRef.value = true
            return
        }
        const storePathNameStorage = createCacheStorage(CacheConfig.StorePathName)
        const pathName = storePathNameStorage.get()
        const params = parseUrlParams(location.search,false)
        const webAuthParams: WXAuthConfig = {
            type: QWAuthTypeEnum.WX,
            appid: storeInfo.appId,
            state: params.state as string
          }
          redirectToWebpageAuthorization(webAuthParams,pathName as string)
          return
    }

    onMounted(async()=>{
        const {name,imgPath} = await getStoreLogo()
        const{sgAppId} = await getStoreLoginWxappID()
        storeInfo.appId = sgAppId 
        storeInfo.name = name
        storeInfo.logoUrl = imgPath
    })
</script>
<style scoped lang="less">
.login-wrapper{
    width: 100vw;
    height: 100vh;
    background-color: #fff;
    background-size: 100% auto;
    background-image: url(/src/assets/store/loginBg.png);
    background-repeat: no-repeat;
    padding: 80px 40px 0px;
    box-sizing: border-box;
    text-align: center;
    .title{
        margin-bottom: 32px;
        font-size: 28px;
        color: #333333;
        font-weight: 600;   
    }
    .store-logo{
        width: 80px;
        height:80px;
        border-radius: 12px;
    }
    .store-name{
        margin-top: 16px;
        font-weight: 600;   
        font-size: 20px;
        color: #333333;
        margin-bottom: 90px;
    }
    .checkbox-wrapper{
        margin-top: 12px;
        color:#666666;
        font-size: 12px;
        line-height: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
        .primary-text{
            color:#1677FF;
            padding:0px 2px;
        }
    }
}
:deep(.van-popover__arrow){
    color:#1677FF;
}


</style>