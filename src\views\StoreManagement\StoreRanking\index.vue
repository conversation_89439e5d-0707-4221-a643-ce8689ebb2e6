<template>
  <JLoadingWrapper :show="isPageLoadingRef">
    <div class="store_order_statistics">
      <div
        class="store_order_background"
        :style="{
          backgroundImage: `url(${rankingBackground})`,
          backgroundSize: 'cover',
        }"
      >
        <div class="store_order_background-title">会员排行榜</div>
        <!-- 副标题 -->
        <div class="store_order_background-subtitle">会员消费金额和下单排名展示</div>
        <div v-if="model.type === OrderSortTypeEnum.AMOUNT">
          <div
            class="store_order_background-tab tab-check"
            :style="{
              backgroundImage: `url(${sumOfConsumptionCheck})`,
              backgroundSize: 'cover',
              left: 0,
            }"
          ></div>
          <div class="store_order_background-tab-title title-check left-title-check">消费金额</div>
          <div
            class="store_order_background-tab"
            :style="{
              backgroundImage: `url(${orderQuantity})`,
              backgroundSize: 'cover',
              right: 0,
            }"
          ></div>
          <div @click.stop="typeClick(2)" class="store_order_background-tab-title left-title">下单数</div>
        </div>
        <div v-else>
          <div
            class="store_order_background-tab"
            :style="{
              backgroundImage: `url(${sumOfConsumption})`,
              backgroundSize: 'cover',
              left: 0,
            }"
          ></div>
          <div @click.stop="typeClick(1)" class="store_order_background-tab-title right-title">消费金额</div>
          <div
            class="store_order_background-tab tab-check"
            :style="{
              backgroundImage: `url(${orderQuantityCheck})`,
              backgroundSize: 'cover',
              right: 0,
            }"
          ></div>
          <div class="store_order_background-tab-title title-check right-title-check">下单数</div>
        </div>
      </div>
      <div class="store_order_ranking">
        <!-- 筛选条件 -->
        <div class="screening_condition_wrapper">
          <div class="screening_condition_header">
            <TagSelectGroup :data="dataRangeList" v-model:value="model.dataRangeRef" />
            <div class="screening_condition_header_right" @click="onShowScreening">
              <span>筛选</span>
              <SvgIcon
                name="dropDown"
                style="font-size: 18px;"
                :class="{'rotate-180': isShowStatisticalScreening, 'icon': true }"
              />
            </div>
          </div>
        </div>
        <!-- 订单信息 -->
        <div class="store_order_statistics_wrapper">
          <div class="store_order_statistics_info">
            <!-- 表头 -->
            <div class="store_order_statistics_info_header">
              <div class="store_order_statistics_info_header_item" style="flex: .8;">排名</div>
              <div class="store_order_statistics_info_header_item" style="flex: 2; padding-left: 6px;">客户</div>
              <div class="store_order_statistics_info_header_item" style="flex: 1;">
                {{ model.type === OrderSortTypeEnum.AMOUNT?'消费金额':'下单数' }}
              </div>
            </div>
            <!-- 表体 -->
            <div class="store_order_statistics_info_body">
              <VanPullRefresh v-model="refreshingRef" @refresh="onRefresh" class="store_order_list_content" @scroll="onScroll">
                <template v-if="ordersList.length">
                  <VanList
                    v-model:loading="isLoadingRef"
                    :finished="isFinishedRef"
                    finished-text="没有更多了"
                    @load="onLoad"
                  >
                    <StoreOrderStatisticsCard
                      v-for="item,index in ordersList"
                      :rank="index + 1"
                      :key="item.id"
                      :item="{...item, type: model.type}"
                      :dataRange="model.dataRangeRef"
                      class="store_order_list_item"
                    />
                  </VanList>
                </template>
                <template v-else>
                  <EmptyData />
                </template>
              </VanPullRefresh>
            </div>
          </div>
        </div>

        <!-- 订单统计筛选 -->
        <JStatisticalScreening
          v-model:show="isShowStatisticalScreening"
          :model="model"
          @confirm="onConfirm"
          :isOrderType="false"
          :isProductName="false"
        />
      </div>
    </div>
  </JLoadingWrapper>
</template>

<script lang="ts" setup>
import { ref, onMounted, onActivated, nextTick } from "vue";
import useGetStoreOrderStatistics from "./hooks/useGetStoreOrderStatistics";
import { KeepAliveRouteNameEnum } from "@/views/StoreModule/enums";
import { useKeepAliveRoute } from '@/hooks/useKeepAliveRoute';
import rankingBackground from "@/assets/storeImage/storeHome/rankingBackground.png";
import orderQuantity from "@/assets/storeImage/storeHome/order_quantity.png";
import orderQuantityCheck from "@/assets/storeImage/storeHome/order_quantity_check.png";
import sumOfConsumptionCheck from "@/assets/storeImage/storeHome/sum_of_consumption_check.png";
import sumOfConsumption from "@/assets/storeImage/storeHome/sum_of_consumption.png";
/** 相关组件 */
import TagSelectGroup from "@/views/StoreModule/components/TagSelectGroup.vue";
import JDateRangePicker from "@/views/StoreModule/components/JDateRangePicker.vue";
import JStatisticalScreening, { type ModelType } from "@/views/StoreModule/components/JStatisticalScreening.vue";
import EmptyData from "@/views/StoreModule/components/EmptyData.vue";
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";
import StoreOrderStatisticsCard from "./components/StoreOrderStatisticsCard.vue";
import { OrderSortTypeEnum } from "@/views/StoreModule/enums";
import { useMessages } from "@/hooks/useMessage";

defineOptions({ name: KeepAliveRouteNameEnum.MEMBER_RANKING });

const { pushKeepAliveRoute, deleteKeepAliveRouteByName, scrollEventHandler, restoreScrollPositionByDom, _findIndex } = useKeepAliveRoute();
const {
  model,
  dataRangeList,
  isPageLoadingRef,
  ordersList,
  isFinishedRef,
  refreshingRef,
  isLoadingRef,
  onLoad,
  onRefresh,
  getStoreOrderStatistics,
  initStoreOrderStatistics
} = useGetStoreOrderStatistics();

const isShowStatisticalScreening = ref(false);

function onShowScreening() {
  isShowStatisticalScreening.value = !isShowStatisticalScreening.value;
}
const { createMessageError } = useMessages();
/** 筛选确认回调 */
function onConfirm(data: ModelType) {
  const { orderType, staffShortId, memberShortId } = data;
  Object.assign(model.value, {
    orderType,
    staffShortId,
    memberShortId,
  });
  if (isNaN(Number(staffShortId))){
    createMessageError('请输入正确的店员编号!')
    return
  }
  if (isNaN(Number(memberShortId))){
    createMessageError('请输入正确的会员编号!')
    return
  }
  initStoreOrderStatistics();
}
function typeClick(type: number) {
  model.value.type = type;

  initStoreOrderStatistics();
}

/** 滚动触发 */
function onScroll(e) {
  console.log(111);
  
  scrollEventHandler(e, KeepAliveRouteNameEnum.MEMBER_RANKING);
}

onActivated(() => {
  nextTick(() => {
    const el = document.getElementsByClassName('store_order_list_content')[0];
    restoreScrollPositionByDom(el, KeepAliveRouteNameEnum.MEMBER_RANKING);
    deleteKeepAliveRouteByName(KeepAliveRouteNameEnum.MEMBER_RANKING);
  });
});

/** 组件挂载 */
onMounted(() => {
  initStoreOrderStatistics();
});
</script>

<style lang="less" scoped>
.store_order_statistics {
  width: 100%;
  background: #F8F8F8;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  .store_order_background {
    height:160px;
    width: 100%;
    position: relative;
    .store_order_background-title{
      font-family: YouSheBiaoTiHei;
      font-weight: 500;
      font-size: 40px;
      color: #FFFFFF;
      text-align: left;
      font-style: normal;
      text-transform: none;
      left: 28px;
      top: 38px;
      position: absolute;
    }
    .store_order_background-subtitle {
      font-size: 16px;
      color: #FFFFFF;
      text-align: left;
      font-style: normal;
      text-transform: none;
      left: 28px;
      top: 80px;
      position: absolute;
    }
    .store_order_background-msg{
      position: absolute;
      top: 90px;
      left: 28px;
      font-weight: 400;
      font-size: 14px;
      color: #FFFFFF;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
    .store_order_background-tab{
      z-index:30;
      position: absolute;
      bottom: -81px;
      width: 290px;
      height: 101px;
    }
    .store_order_background-tab-title{
      z-index:999;
      position: absolute;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 500;
      font-size: 20px;
      color: #333333;
      line-height: 26px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      min-width: 10px;
      min-height: 10px;
      top: 145px;
    }
    .title-check{
      font-weight: 500;
      font-size: 20px;
    }
    .left-title{
      right:56px;
    }
    .left-title-check{
      left:64px;
    }
    .right-title{
      left:56px;
    }
    .right-title-check{
      right:64px;
    }
    .tab-check{
      z-index:50;
      bottom: -161px;
      height: 206px;
      width: 100% !important;
    }
  }
  .store_order_ranking{
    padding-top: 34px;
    z-index:100;
    .statistics-notice {
      height: 36px;
      flex-shrink: 0;
      .notice-title {
        font-weight: 400;
        font-size: 14px;
        color: #1677FF;
        line-height: 22px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }

    .screening_condition_wrapper {
      padding: 12px;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      flex-shrink: 0;

      .screening_condition_header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .screening_condition_header_right {
          display: flex;
          align-items: center;

          span {
            font-weight: 400;
            font-size: 14px;
            color: #666666;
            line-height: 20px;
            text-align: right;
            font-style: normal;
            text-transform: none;
          }

          .icon {
            transition: all 0.3s;
          }
        }
      }
    }

    .store_order_statistics_wrapper {
      width: 100%;
      padding: 0px 12px 12px 12px;
      box-sizing: border-box;
      .store_order_statistics_info {
        flex: 1;
        display: flex;
        flex-direction: column;
        background-color: #FFFFFF;
        box-sizing: border-box;
        border-radius: 12px;
  
        .store_order_statistics_info_header {
          display: flex;
          align-items: center;
          padding: 12px 12px 0px 12px;
          box-sizing: border-box;
          margin-top: 16px;
  
          .store_order_statistics_info_header_item {
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 500;
            font-size: 14px;
            color: #666666;
            line-height: 18px;
            text-align: left;
            font-style: normal;
            text-transform: none;
          }
        }
  
        .store_order_statistics_info_body {
          flex: 1;
          box-sizing: border-box;
  
          .store_order_list_content {
            height: calc(100vh - 236px);
            box-sizing: border-box;
            overflow-y: auto;
          }
        }
      }
    }
  }

}

.rotate-180 {
  transform: rotate(-180deg);
}
</style>
