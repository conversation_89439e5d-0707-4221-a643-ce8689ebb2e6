import { ref, computed } from "vue";
import { isNUllString } from "@/utils/isUtils";
import { useMessages } from "@/hooks/useMessage";
import { PayTypeEnum, StoreGoodsEnum } from "@/enums/storeGoods";
import { isInFrame, isIOSEnv } from "@/utils/envUtils";
import { MessageEventEnum, useWindowMessage } from '@/hooks/useWindowMessage';
import {
  getOrderPayStatus,
  OrderPay,
  queryOrderPayment,
  getRequestNo,
  pointQueryOrderPayment,
  pointOrderPay,
} from "@/services/storeApi";

interface ResponseObject {
  /** 公众号ID */
  appId?: null | string;
  /** 随机字符串 */
  nonceStr?: null | string;
  /** 订单详情扩展字符串 */
  package?: null | string;
  /** 签名 */
  paySign?: null | string;
  /** 签名方式 */
  signType?: null | string;
  /** 时间戳 */
  timeStamp?: null | string;
  /** 其他属性 */
  [property: string]: any;
}

/**
 * 支付功能自定义Hook
 * @param type - 商品类型（普通商品或积分商品）
 * @returns 支付相关方法和状态
 */
export function usePayment(type: StoreGoodsEnum) {

  const { sendMessageToWindows } = useWindowMessage()
  const message = useMessages();
  /** 是否正在处理支付状态 */
  const isPendingStatus = ref(false);
  /** 幂等性请求ID（防止重复提交） */
  const requestNoRef = ref("");
  /** 订单编号 */
  const orderCodeRef = ref("");
  /** 支付成功返回地址 */
  const pageNotifyUrlRef = ref<string>("");
  /** 重复请求计数器 */
  const reuseNum = ref(0);

  /** 根据商品类型计算支付方式 */
  const payType = computed<PayTypeEnum>(() => {
    console.log("type", type);
    
    if (type == StoreGoodsEnum.Goods) {
      // 普通商品使用在线支付
      return PayTypeEnum.OnlinePay; 
    }
    if (type == StoreGoodsEnum.IntegralGoods) {
      // 积分商品使用积分+在线支付
      return PayTypeEnum.PointAndOnlinePay; 
    }
  });

  /**
   * 调起微信支付
   * @param response - 预支付返回参数
   * @param orderCode - 订单编号
   * @returns Promise 支付成功返回true，失败或取消返回错误信息
   */
  const paymentFn = (response: ResponseObject, orderCode: string): Promise<boolean> => {
    return new Promise((resolve, reject) => {
      // 调用微信JSBridge发起支付
      WeixinJSBridge.invoke(
        "getBrandWCPayRequest",
        {
          appId: response.appId, // 公众号ID
          timeStamp: response.timeStamp, // 时间戳
          nonceStr: response.nonceStr, // 随机串
          package: response.packageVal, // 订单详情
          signType: response.signType, // 签名方式
          paySign: response.paySign, // 签名
        },
        function (res) {
          console.log("微信支付回调结果：", res);

          // 处理支付结果
          if (res.err_msg == "get_brand_wcpay_request:ok") {
            // 支付成功
            resolve(true); 
          } else if (res.err_msg == "get_brand_wcpay_request:cancel") {
            // 用户取消支付，重置重复计数器
            reuseNum.value = 0;
            reject("用户取消支付");
          } else if (res.err_msg == "get_brand_wcpay_request:fail") { 
            // 支付失败
            reject("支付失败");
          }
        },
      );
    });
  };

  /**
   * 查询支付状态（带重试机制）
   * @param orderCode - 要查询的订单编号
   * @returns Promise<boolean> - 返回查询是否成功
   */
  const getPayStatusFn = async (orderCode: string): Promise<boolean> => {
    return new Promise((resolve, reject) => {
      isPendingStatus.value = true; // 标记为处理中状态
      let retryCount = 0; // 重试计数器
      let shouldContinue = true; // 控制是否继续轮询
      let timeoutId: NodeJS.Timeout | null = null; // 保存定时器ID用于清理

      // 清理函数
      const cleanup = () => {
        shouldContinue = false;
        isPendingStatus.value = false;
        if (timeoutId) {
          clearTimeout(timeoutId);
          timeoutId = null;
        }
      };

      // 定义查询函数
      const getStatus = () => {
        if (!shouldContinue) return;

        getOrderPayStatus({ orderCode })
          .then(res => {
            console.log(res, "支付状态查询结果");
            if (!isNUllString(res.state)) {
              showStatusPopup(res.state); // 显示支付状态
              cleanup();
              resolve(true); // 成功获取状态
            } else {
              throw new Error("未获取到支付状态");
            }
          })
          .catch(err => {
            console.error("支付状态查询失败:", err);
            retryCount += 1;
            if (retryCount < 5) {
              // 5次内自动重试
              timeoutId = setTimeout(() => {
                getStatus();
              }, 1000);
            } else {
              // 超过5次改用主动查询作为最后尝试
              cleanup();
              getQueryOrderPayment(orderCode)
                .then(() => resolve(true))
                .catch(finalErr => {
                  console.error("最终查询失败:", finalErr);
                  message.createMessageError("支付状态查询失败，请手动刷新页面查看结果");
                  reject(finalErr);
                });
            }
          });
      };

      getStatus(); // 开始查询支付状态
    });
  };

  /**
   * 主动查询支付状态（用于轮询失败后的备用方案）
   * @param orderCode - 要查询的订单编号
   */
  const getQueryOrderPayment = async (orderCode: string) => {
    try {
      const params = {
        orderCode,
        payType: payType.value, // 使用计算出的支付方式
      };

      // 根据商品类型选择对应的API
      const api =
        type == StoreGoodsEnum.IntegralGoods
          ? pointQueryOrderPayment // 积分商品API
          : queryOrderPayment; // 普通商品API

      const res = await api(params);
      showStatusPopup(res.state);
    } catch (error) {
      showStatusPopup(); // 显示默认状态
    }
  };

  /**
   * 执行微信支付流程
   */
  const WXPayFn = async () => {
    try {
      const params = {
        transno: requestNoRef.value, // 幂等性ID
        data: {
          orderCode: orderCodeRef.value, // 订单编号
          payType: payType.value, // 支付方式
          pageNotifyUrl: pageNotifyUrlRef.value, // 支付成功返回地址
        },
      };

      isPendingStatus.value = true; // 标记为处理中

      // 根据商品类型选择对应的支付API
      const api =
        type == StoreGoodsEnum.IntegralGoods
          ? pointOrderPay // 积分商品支付
          : OrderPay; // 普通商品支付

      const res = await api(params);
      isPendingStatus.value = false;

      // 调起微信支付
      await paymentFn(res.response, res.orderCode);
      // 查询支付状态
      getPayStatusFn(res.orderCode);
    } catch (error) {
      console.log("微信支付捕获错误", error);
      setTimeout(() => {
        isTwo409(error); // 处理409错误
      }, 300);
    } finally {
      isPendingStatus.value = false; // 重置处理状态
    }
  };

  /**
   * 显示支付状态弹窗
   * @param status - 支付状态（默认为空）
   */
  const showStatusPopup = (status: string = "") => {
    isPendingStatus.value = false;
    if (status == "SUCCESS") {
      message.createMessageSuccess("支付成功");
      if(isInFrame()){ 
        if(isIOSEnv()){
          sendMessageToWindows(MessageEventEnum.sendUrlToWindow, pageNotifyUrlRef.value);
        } else {
          window.location.href = pageNotifyUrlRef.value;
        }
      } else{
        window.location.href = pageNotifyUrlRef.value;
      }
    }
  };

  /**
   * 处理409错误（幂等性错误）
   * @param err - 错误对象
   */
  const isTwo409 = err => {
    // 检查是否是幂等性或重复提交错误
    if (err?.includes("无幂等性") || err?.includes("已重复提交")) {
      if (reuseNum.value < 1) {
        reuseNum.value += 1; // 增加重试计数
        GetRequestNoFn(); // 获取新的请求编号并重试
      } else {
        reuseNum.value = 0; // 重置计数器
        message.createMessageError(`${err}`);
      }
    } else {
      // 其他错误直接显示
      message.createMessageError(err);
    }
  };

  /**
   * 获取新的请求编号（用于幂等性控制）
   */
  const GetRequestNoFn = async () => {
    try {
      const res = await getRequestNo();
      requestNoRef.value = res; // 更新请求编号
      WXPayFn(); // 使用新编号重新发起支付
    } catch (error) {
      message.createMessageError(`获取幂等性ID失败${error}`);
    }
  };

  return {
    /** 微信支付方法 */
    WXPayFn,
    /** 是否正在处理中 */
    isPendingStatus,
    /** 幂等性请求ID */
    requestNoRef,
    /** 订单编号引用 */
    orderCodeRef,
    /** 支付通知地址 */
    pageNotifyUrlRef,
  };
}
