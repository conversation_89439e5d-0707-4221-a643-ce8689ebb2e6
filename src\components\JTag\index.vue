<template>
    <div 
        :class="[
            'j-tag',
            props.size,
            props.type,
            {'strong':props.strong},
            {'border':props.border},
            {'round':props.round},
            {'block':props.block}
        ]
    ">
        <van-loading v-if='props.loading' color="#1989fa" size="14"/><slot v-else></slot>
    </div>
</template>
<script setup lang="ts">

type JTagProps = {
    type:'primary' | 'inactive' |'danger' | 'success' | 'warning',
    loading?:boolean,
    size?:'small' | 'large' | 'middle',
    strong?:boolean,
    border?:boolean,
    round?:boolean,
    block?:boolean
}

const props = withDefaults(defineProps<JTagProps>(),{
    type:'primary',
    loading:false,
    size:'small',
    strong:false,
    border:false,
    round:false,
    block:false
})

</script>
<style scoped lang="less">
@import "@/styles/defaultVar.less";
    .j-tag{
        // display: flex;
        // justify-content: center;
        // align-items: center;
        padding: 0 10px;
        text-align: center;
        font-size: 14px;
        display:inline-block;
        box-sizing: border-box;
        border-radius: 5px;
        &.block{
            display: block;
        }
        &.round{
            border-radius: 12px;
        }
    }
    .small{
        height: 26px;
        line-height: 26px;
    }
    .middle{
        height: 30px;
        line-height: 30px;
    }
    .primary{
        background-color:#E7F1FF;
        color:@primary-color;
        &.strong{
            background-color:@primary-color;
            color:#fff;
        }
        &.border{
            border:1px solid @primary-color;
        }
    }
    .success{
        background-color: #E5F7E9;
        color:#00B42A;
        &.border{
            border:1px solid #00B42A;
        }
    }
    .warning{
        background-color: #FFF3E5;
        color:#FF8A00;
        &.border{
            border:1px solid #FF8A00;
        }
    }
    .danger{
        background-color: #FFECEC;
        color:@error-color;
        &.border{
            border:1px solid @error-color;
        }
    }
    .inactive{
        background-color:#F8F8F8;
        color:#999999;
        &.strong{
            background-color:#fff;
            color:#999999;
        }
    }

</style>