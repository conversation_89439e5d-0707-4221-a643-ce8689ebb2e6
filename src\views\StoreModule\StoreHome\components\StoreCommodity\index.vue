<template>
  <div class="store-commodity-wrapper">
    <!-- header -->
    <div class="store-commodity-header">
      <div class="line"></div>
      <div class="store-commodity-header-title">最新商品</div>
      <div class="line"></div>
    </div>
    <div class="store-commodity-list">
      <template v-if="commodityList.length">
        <!-- 使用普通的 div 包裹瀑布流 -->
        <div ref="listRef" class="waterfall-container">
          <!-- 左列 -->
          <div class="waterfall-column">
            <StoreCommodityCard
              v-for="item in leftColumnItems"
              :key="item.id"
              :productInfo="item"
              @click="handleClickToStoreDetail(item)"
            />
          </div>
          <!-- 右列 -->
          <div class="waterfall-column">
            <StoreCommodityCard
              v-for="item in rightColumnItems"
              :key="item.id"
              :productInfo="item"
              @click="handleClickToStoreDetail(item)"
            />
          </div>
        </div>
        <!-- 加载更多提示 -->
        <div v-if="isLoadingRef" class="loading-more">加载中...</div>
        <div v-if="isFinishedRef" class="no-more">没有更多了</div>
      </template>
      <template v-else>
        <EmptyData />
      </template>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, watch } from "vue";
import { useRouterUtils } from "@/views/StoreModule/hooks";
import { RoutesName } from "@/enums/routes";
import { StoreGoodsEnum } from "@/enums/storeGoods";
import { KeepAliveRouteNameEnum } from "@/views/StoreModule/enums";
import { useKeepAliveRoute } from '@/hooks/useKeepAliveRoute';
import useGetStoreCommodity from "./hooks/useGetStoreCommodity";
/** 相关组件 */
import EmptyData from "@/views/StoreModule/components/EmptyData.vue";
import StoreCommodityCard from "./components/StoreCommodityCard.vue";

defineOptions({ name: 'StoreCommodity' });

/** props */
const props = defineProps<{
  storeId: string;
}>();

const { pushKeepAliveRoute } = useKeepAliveRoute();
const { routerPushByRouteName } = useRouterUtils();
const {
  refreshingRef,
  isLoadingRef,
  isFinishedRef,
  commodityList,
  onRefresh,
  onLoad,
  initStoreCommodity
} = useGetStoreCommodity({
  storeId: props.storeId
});

const listRef = ref<HTMLElement | null>(null);

/** 计算属性，将商品列表分成左右两列 */
const leftColumnItems = computed(() => {
  return commodityList.value.filter((_, index) => index % 2 === 0);
});

const rightColumnItems = computed(() => {
  return commodityList.value.filter((_, index) => index % 2 === 1);
});

/** 跳转到商品详情 */
function handleClickToStoreDetail(item) {
  pushKeepAliveRoute(KeepAliveRouteNameEnum.HOME);
  routerPushByRouteName(RoutesName.StoreDetail, { id: item.id, type: StoreGoodsEnum.Goods });
}

defineExpose({
  initStoreCommodity,
  onLoad,
  isLoadingRef,
  isFinishedRef
});
</script>

<style lang="less" scoped>
.store-commodity-wrapper {
  width: 100%;
  height: 100%;
  .store-commodity-header {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 12px 0px;
    .line {
      width: 56px;
      height: 1px;
      background: rgba(0,0,0,0.1);
      transform: scaleY(0.6);
      transform-origin: 0 0;
    }
    .store-commodity-header-title {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 500;
      font-size: 18px;
      color: #333333;
      line-height: 24px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      margin: 0 12px;
    }
  }
  .store-commodity-list {
    height: 100%;
    min-height: 300px;
    background-color: #F8F8F8;
    padding: 6px 12px;
    box-sizing: border-box;
    
    .waterfall-container {
      display: flex;
      gap: 8px;
      
      .waterfall-column {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 8px;
      }
    }
    
    .loading-more, .no-more {
      text-align: center;
      padding: 10px 0;
      color: #999;
      font-size: 14px;
    }
  }
}
</style>