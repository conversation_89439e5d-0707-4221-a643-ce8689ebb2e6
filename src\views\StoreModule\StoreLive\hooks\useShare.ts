import {
  getLiveWatchTimeCouponList,
  getShareCoupon,
  type ListWatchTimeCouponResponse,
  type LiveCouponInfoResponse,
  receiveCoupon,
  receiveShareCoupon,
  type receiveCouponVo,
} from "@/services/storeApi";
import { ref } from "vue";
import { useUserStoreWithoutSetup } from "@/stores/modules/user";
const userStore = useUserStoreWithoutSetup();
import { createCacheStorage } from "@/utils/cache/storageCache";
import { CacheConfig } from "@/utils/cache/config";

interface ShareCouponData {
    /** 领取标志 0=未领取，1=已领取  2=停止发放 */
  receiveFlag: number;
  /** 优惠券ID */
  couponBatchId: number;
  /** 优惠券分类名称 */
  categoryName: string;
  /** 优惠券图片 */
  imageUrl: string;
}

// 分享券
const shareCouponData = ref<ShareCouponData>({} as ShareCouponData);
// 是否播放
const isPlay = ref(false);

export default function useShare() {
  /** 获取分享券 */
  async function getShareCouponData(liveRoomId) {
    try {
      let result = await getShareCoupon(liveRoomId);
      shareCouponData.value = result;
    } catch (err) {
      console.log(`获取分享券数据失败:${err}`);
      throw new Error("获取分享券数据失败");
    }
  }

  /** 领取分享券 */
  async function handleReceiveShareCoupon(liveRoomId) {
    if (!isPlay.value) return;
    await getShareCouponData(liveRoomId);
    //  如果已结束或者已领取则不再请求接口
    if (!shareCouponData.value.couponBatchId ||  shareCouponData.value.receiveFlag != 0 ) return;
    try {
      const params = {
        liveRoomId: liveRoomId,
        couponBatchId: shareCouponData.value.couponBatchId,
      };
      await receiveShareCoupon(params);
      shareCouponData.value.receiveFlag = 1;
      // 更新缓存
      userStore.liveCouponReceivedCache[liveRoomId] = shareCouponData.value;
      userStore.setLiveCouponReceivedCache(userStore.liveCouponReceivedCache);
    } catch (err) {
        shareCouponData.value.receiveFlag = 2;
        console.log(`领取分享券失败:${err}`);
        throw new Error("领取分享券失败");
    }
  }

  return {
    shareCouponData,
    getShareCouponData,
    handleReceiveShareCoupon,
    isPlay
  };
}
