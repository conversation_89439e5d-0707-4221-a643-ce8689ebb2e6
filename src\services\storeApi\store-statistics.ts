import { defHttp } from "@/services";
import { getStoreApiUrl  } from "@/utils/http/urlUtils";
const enum StorStatisticsApi{
    couponStatDataPage = "/h5/store/personal/couponStatDataPage",//福利券统计
    couponDetailStatDataPage = "/h5/store/personal/couponDetailStatDataPage",//福利券统计详情
    queryWriteOffStatistics = "/h5/store/personal/queryWriteOffStatistics",//核销统计
    productSellStatDataPage = "/h5/store/personal/productSellStatDataPage",//销售统计
    staffSurveyStatData = "/h5/store/personal/staffSurveyStatData",//店员概览数据统计
    staffStatDataPage= "/h5/store/personal/staffStatDataPage",//店员数据统计分页
    pageProductSellStatDataDetail = "/h5/store/personal/pageProductSellStatDataDetail", // 门店管理-店员/店长商品销售订单详情分页统计
}

/**
 * @description 门店管理-店员/店长商品销售订单详情分页统计
 */
export function pageProductSellStatDataDetail(params:GetStatisticsPageParams){
    return defHttp.post({
        url: getStoreApiUrl(StorStatisticsApi.pageProductSellStatDataDetail),
        params:params,
        requestConfig: {
            skipCrypto:true
        },
    });
}
interface GetStatisticsPageParams{
    data:{
        staffId?:string,
        csId?:string,
    },
    pageVO:{
        current:number,
        size:number,
    }
}
export function getCouponStatDataPage(params:GetStatisticsPageParams){
    return defHttp.post({
        url: getStoreApiUrl(StorStatisticsApi.couponStatDataPage),
        params:params,
        requestConfig: {
            skipCrypto:true
        },
    });
}

export function getCouponDetailStatDataPage(params){
    return defHttp.post({
        url: getStoreApiUrl(StorStatisticsApi.couponDetailStatDataPage),
        params:params,
        requestConfig: {
            skipCrypto:true
        },
    });
}

export function getqueryWriteOffStatistics(params){
    return defHttp.post({
        url: getStoreApiUrl(StorStatisticsApi.queryWriteOffStatistics),
        params:params,
        requestConfig: {
            skipCrypto:true
        },
    });
}

export function getproductSellStatDataPage(params){
    return defHttp.post({
        url: getStoreApiUrl(StorStatisticsApi.productSellStatDataPage),
        params:params,
        requestConfig: {
            skipCrypto:true
        },
    });
}

export function getstaffSurveyStatData(params){
    return defHttp.post({
        url: getStoreApiUrl(StorStatisticsApi.staffSurveyStatData),
        params:params,
        requestConfig: {
            skipCrypto:true
        },
    });
}

export function getstaffStatDataPage(params){
    return defHttp.post({
        url: getStoreApiUrl(StorStatisticsApi.staffStatDataPage),
        params:params,
        requestConfig: {
            skipCrypto:true
        },
    });
}