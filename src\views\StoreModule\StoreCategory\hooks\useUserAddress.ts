import { ref, reactive, computed } from "vue";
import { useMessages } from "@/hooks/useMessage";
import { getAddressById } from "@/services/storeApi";

/** 当前选中地址id */
const currentAddressInfo = ref(null);

export default function useUserAddress() {
  const { createMessageError, createMessageSuccess } = useMessages();
  /** 默认地址 */
  const defaultAddress = reactive({
    name: "",
    mobile: "",
    companyId: "",
    company: "",
    provinceId: "",
    province: "",
    cityId: "",
    cityName: "",
    areaId: "",
    area: "",
    townId: "",
    town: "",
    address: "",
    isDefault: 1,
    id: "",
    csWxNickname: "",
    type: 0
  });

  /** 是否存在默认地址 */
  const isDefaultAddress = computed(() => {
    const { name, mobile, province } = defaultAddress;
    return name && mobile && province;
  });

  /** 根据id查询地址 */
  async function queryAddressById(id: string) {
    try {
        const _params = {
          id,
        };
      const resp = await getAddressById(_params);
      if (resp) {
        return resp;
      }
    } catch (error) {
      createMessageError("查询地址失败：" + error);
      return null;
    }
  }

  return {
    currentAddressInfo,
    queryAddressById,
    defaultAddress,
    isDefaultAddress
  };
}
