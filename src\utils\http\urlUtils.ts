import { isProdEnv, isTestEnv } from "@/utils/envUtils";
import { isString } from "../isUtils";
import { createCacheStorage } from "../cache/storageCache";
import { CacheConfig } from "../cache/config";

/**
 * 解析 url 中的参数
 * @param url url
 * @param parseUserIdFromState 是否从state解析userId
 * @returns {Object}
 */
function parseUrlParams(url,parseUserIdFromState = true) {
  const params:Record<string,string|boolean> = {};
  if (!url || url === "" || typeof url !== "string") {
    return params;
  }
  const paramsStr = url.split("?")[1];
  if (!paramsStr) {
    return params;
  }
  const paramsArr = paramsStr.replace(/&|=/g, " ").split(" ");
  for (let i = 0; i < paramsArr.length / 2; i++) {
    const value = paramsArr[i * 2 + 1];
    params[paramsArr[i * 2]] = value === "true" ? true : value === "false" ? false : value;
  }
  if(isString(params['state'])){
    if(parseUserIdFromState){
      const stateArr = `${params['state']}`.replace(/_tsstr_\d+$/, '').split('_userId_');
      params['state'] = stateArr[0];
      params['userId'] = stateArr[1];
    }else{
      params['state'] = `${params['state']}`.replace(/_tsstr_\d+$/, '');
    }
    
  }
  return params;
}

function getURLPrefix(url?: string) {
  if (url) {
    return "";
  }
  const isProd = isProdEnv();
  const PROFIX = isProd ? `${import.meta.env.VITE_API_URL}` : import.meta.env.VITE_DEV_PREFIX;
  return `${PROFIX}`;
}

function getOssUrlPrefix(){
  const isTest = isTestEnv();
  const isProd = isProdEnv();
  if(isTest){
    return `${import.meta.env.VITE_API_URL}`
  }
  else if(isProd){
    const hostnameSplit = location.hostname.split(".");
    const firstPart = hostnameSplit[0].endsWith("-m") ? hostnameSplit[0].slice(0, -2) : hostnameSplit[0];
    hostnameSplit[0] = `o`;
    const prefix = `${location.protocol}//${hostnameSplit.join(".")}`
    return prefix
  }
  else{
    return import.meta.env.VITE_DEV_PREFIX
  }
}

function getApiUrlPrefix(){
  const isTest = isTestEnv();
  const isProd = isProdEnv();
  if(isTest){
    return `${import.meta.env.VITE_API_URL}`
  }
  else if(isProd){
    const apiPrefixStorage = createCacheStorage(CacheConfig.ApiPrefix)
    const apiPrefix = apiPrefixStorage.get('api')
    if(apiPrefix){
      return `${apiPrefix}`
    }
    else{
      const hostnameSplit = location.hostname.split(".");
      const firstPart = hostnameSplit[0].endsWith("-m") ? hostnameSplit[0].slice(0, -2) : hostnameSplit[0];
      hostnameSplit[0] = `a`;
      const prefix = `${location.protocol}//${hostnameSplit.join(".")}`
      return prefix
    }
  }
  else{
    return import.meta.env.VITE_DEV_PREFIX
  }
}

function getImgUrlPrefix(){
  const isTest = isTestEnv();
  const isProd = isProdEnv();
  if(isTest){
    return `${import.meta.env.VITE_API_URL}`
  }
  else if(isProd){
    const apiPrefixStorage = createCacheStorage(CacheConfig.ApiPrefix)
    const apiPrefix = apiPrefixStorage.get('api')
    if(apiPrefix){
      return `${apiPrefix}`
    }
    else{
      const hostnameSplit = location.hostname.split(".");
      const firstPart = hostnameSplit[0].endsWith("-m") ? hostnameSplit[0].slice(0, -2) : hostnameSplit[0];
      hostnameSplit[0] = `a`;
      const prefix = `${location.protocol}//${hostnameSplit.join(".")}`
      return prefix
    }
  }
  else{
    return import.meta.env.VITE_DEV_BASE_URL
  }
}


function getRedirectUrl(){
  const isTest = isTestEnv();
  const isProd = isProdEnv();
  if(isProd){
    return location.origin
  }
  else{
    return `${import.meta.env.VITE_REDIRECT_URL}`
  }
}

function getStoreBasicUrl(url){
  if (url.includes('8858')) return url.replace('8858', '8868')
  if (url.includes('store')) return url.replace('store', 'basic')
}
function getStoreApiPrefix(){
  const apiPrefixStorage = createCacheStorage(CacheConfig.ApiPrefix)
  const storeApiPrefix = apiPrefixStorage.get('storeApi')
  return isProdEnv()?`${storeApiPrefix}`:`${import.meta.env.VITE_STORE_DEV_PREFIX}`
}

function getStoreApiUrl(url:string){
  const prefix = getStoreApiPrefix()
  return `${prefix}${url}`
}
function isStoreApi(url:string):boolean{
  const storeApiPrefix = getStoreApiPrefix()
  return url.startsWith(storeApiPrefix)

}



export { getStoreApiPrefix,getStoreApiUrl,isStoreApi,parseUrlParams, getURLPrefix,getOssUrlPrefix,getApiUrlPrefix,getImgUrlPrefix,getRedirectUrl , getStoreBasicUrl };
