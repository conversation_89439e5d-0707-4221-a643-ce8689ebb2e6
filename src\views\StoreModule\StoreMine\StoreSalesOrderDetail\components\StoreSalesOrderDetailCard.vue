<template>
  <div class="order-card">
    <!-- 商品总数 -->
    <div class="order-card__quantity">
      <span>{{ productNum }}</span>
    </div>
    <!-- 商品单价 -->
    <div class="order-card__price">
      <span>{{ price ? (price / 100).toFixed(2) : '-' }}</span>
    </div>
    <!-- 商品活动价 -->
    <div class="order-card__amount">
      <span>{{ activityPrice ? (activityPrice / 100).toFixed(2) : '-' }}</span>
    </div>
    <!-- 订单数 -->
    <div class="order-card__other">
      <span>{{ orderNum }}</span>
    </div>
    <!-- 订单总金额 -->
    <div class="order-card__other">
      <span>{{ totalAmount ? (totalAmount / 100).toFixed(2) : '0.00' }}</span>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { toRefs } from 'vue';
import rightIcon from "@/assets/storeImage/storeMine/rightIcon.png";

defineOptions({ name: 'StoreSalesOrderDetailCard' });

/** props */
const props = defineProps<{
  orderInfo: {
    productId?: string;
    productName?: string;
    specId?: number;
    specName?: string;
    price?: number;
    activityPrice?: number;
    productNum?: string;
    orderNum?: string;
    totalAmount?: number;
  };
}>();

const {
  productNum,
  orderNum,
  totalAmount,
  price,
  activityPrice
} = toRefs(props.orderInfo);
</script>

<style lang="less" scoped>
.order-card {
  display: flex;
  align-items: center;
  height: 64px;
  width: 100%;

  &>div {
    box-sizing: border-box;
    padding: 0 8px;

    span {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 12px;
      color: #333333;
      line-height: 16px;
      text-align: left;
      display: block;
    }
  }

  &__product {
    width: 120px;
    padding-left: 8px;

    .product-name {
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
      height: 100%;
      word-break: break-all;
    }
  }

  &__quantity,
  &__price,
  &__amount,
  &__other {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  &__other {
    display: flex;
    align-items: center;
    gap: 4px;
  }
  &__icon {
    width: 18px;
    height: 18px;
    margin-left: 2px;
  }
}
</style>
