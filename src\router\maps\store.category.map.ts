import { RoutesName } from "@/enums/routes";
import type { RouteLocation } from "vue-router";

export const StoreCategory = {
  [RoutesName.StoreCategory]: {
    path: "category",
    component: () => import("@/views/StoreModule/StoreCategory/index.vue"),
    meta: {
      isMenu: true,
      title: "分类",
      level:2,
      icon: {
        active: "/icons/storeTabbar/cate-active.png",
        inactive: "/icons/storeTabbar/cate.png",
      },
    },
    props: (route: RouteLocation) => ({}),
  },
  [RoutesName.StoreSearch]: {
    path: "search",
    component: () => import("@/views/StoreModule/StoreCategory/GoodsSearch/index.vue"),
    meta: {
      title: "搜索",
      isMenu: false,
      isShow: false,
    },
    props: (route: RouteLocation) => ({
      type: route.query.type,
    }),
  },
  [RoutesName.StoreDetail]: {
    path: "goodsDetail",
    component: () => import("@/views/StoreModule/StoreCategory/GoodsDetail/index.vue"),
    meta: {
      title: "商品详情",
      isMenu: false,
      isShow: false,
    },
    props: (route: RouteLocation) => ({
      id: route.query.id,
      type: route.query.type,
      liveId: route.query.liveId,
    }),
  },
  [RoutesName.StoreIntegralMall]: {
    path: "goodsIntergral",
    component: () => import("@/views/StoreModule/StoreCategory/IntergralGoods/index.vue"),
    meta: {
      title: "积分商城",
      isMenu: false,
      isShow: false,
    },
    props: (route: RouteLocation) => ({}),
  },
  [RoutesName.StoreWelfareVoucherMall]: {
    path: "welfareVoucherMall",
    component: () => import("@/views/StoreModule/StoreCategory/WelfareGoods/index.vue"),
    meta: {
      title: "福利商城",
      isMenu: false,
      isShow: false,
    },
    props: (route: RouteLocation) => ({}),
  },
  [RoutesName.StoreIntegralMallCanExchange]: {
    path: "goodsIntergralExchange",
    component: () => import("@/views/StoreModule/StoreCategory/IntergralGoods/ExchangeList/index.vue"),
    meta: {
      title: "我能兑",
      isMenu: false,
      isShow: false,
    },
    props: (route: RouteLocation) => ({}),
  },
  [RoutesName.StoreConfirmOrder]: {
    path: "confirmOrder",
    component: () => import("@/views/StoreModule/StoreCategory/ConfirmOrder/index.vue"),
    meta: {
      title: "确认订单",
      isMenu: false,
      isShow: false,
    },
    props: (route: RouteLocation) => ({
      type: route.query.type,
      orderInfo: route.query.orderInfo,
      liveId: route.query.liveId,
    }),
  },
  [RoutesName.StoreCashier]: {
    path: "cashier",
    component: () => import("@/views/StoreModule/StoreCategory/CashRegister/index.vue"),
    meta: {
      title: "收银台",
      isMenu: false,
      isShow: false,
    },
    props: (route: RouteLocation) => ({
      type: route.query.type,
      orderCode: route.query.orderCode,
    }),
  },
  [RoutesName.StoreCashCoupon]: {
    path: "cashCoupon",
    component: () => import("@/views/StoreModule/StoreCategory/CashCoupon/index.vue"),
    meta: {
      title: "现金券",
      isMenu: false,
      isShow: false,
    },
    props: (route: RouteLocation) => ({
      orderInfo: route.query.orderInfo,
    }),
  },
  [RoutesName.StorePickUpLocation]: {
    path: "pickUpLocation",
    component: () => import("@/views/StoreModule/StoreCategory/PckUpLocation/index.vue"),
    meta: {
      title: "选择自提点",
      isMenu: false,
      isShow: false,
    },
    props: (route: RouteLocation) => ({
      orderInfo: route.query.orderInfo,
      type: route.query.type,
    }),
  },
};
