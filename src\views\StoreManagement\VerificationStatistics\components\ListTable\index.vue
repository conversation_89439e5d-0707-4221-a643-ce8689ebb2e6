<template>
  <div class="warrper">
    <div class="title">
      <span class="name">商品名</span>
      <span class="totalStyle">商品总数</span>
      <span class="numStyle">订单数</span>
      <span class="amountStyle">订单总额</span>
    </div>
    <div class="boday">
      <VanList
        :offset="50"
        v-model:loading="groupMgrListStatusReactive.isNextPageLoading"
        @load="onGroupMgrListNextPageLoad"
        :finished="groupMgrListStatusReactive.isNextPageFinished"
        finished-text="没有更多了"
      >
        <div class="item" v-for="data in listDatas">
          <span class="name">{{ `${data.productName}(${data.specName})` }}</span>
          <span class="totalStyle">{{ data.productNum }}</span>
          <span class="numStyle">{{ data.orderNum }}</span>
          <span class="amountStyle">
            {{ data?.totalAmount ? (data.totalAmount / 100).toFixed(2) : '0.00' }}
          </span>
        </div>
      </VanList>
    </div>
  </div>
</template>
<script setup lang="ts">
import { VerificationStatisticsList } from '@/views/StoreManagement/VerificationStatistics/hooks/index';

type ListCardProps = {
    listDatas: any,
    searchParam: any
}
const props = withDefaults(defineProps<ListCardProps>(), {
    listDatas: [],
    searchParam: {}
})
const { groupMgrListStatusReactive, onGroupMgrListNextPageLoad } = VerificationStatisticsList(props.searchParam)
</script>

<style scoped lang="less">
.warrper {
    padding: 10px;
    height: calc(100% - 20px);

    .title {
        display: flex;
        align-items: center;
        border-bottom: 1px solid #EEEEEE;
        padding: 10px;
        font-size: 14px;
        font-weight: 500;
        color: #333;
    }

    .boday {
        height: calc(100% - 35px);
        overflow-y: auto;

        .item {
            display: flex;
            align-items: center;
            padding: 10px;
            font-size: 13px;
            border-bottom: 1px solid #f5f5f5;
        }

        .item:nth-child(2n -1) {
            background-color: #FFF4F4;
        }

        .item:hover {
            background-color: #f9f9f9;
        }
    }

    .name {
        width: 45%;
        /* 商品名宽度增加 */
        display: -webkit-box;
        -webkit-line-clamp: 3;
        /* 控制显示行数 */
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 1.4;
        padding-right: 8px;
    }

    .totalStyle {
        width: 20%;
        /* 商品总数 */
        text-align: center;
    }

    .numStyle {
        width: 15%;
        /* 订单数 */
        text-align: center;
    }

    .amountStyle {
        width: 20%;
        /* 订单总额 */
        text-align: right;
        padding-right: 5px;
    }

    /* 为标题和内容设置相同的对齐方式 */
    .title .name,
    .item .name {
        text-align: left;
    }

    .title .totalStyle,
    .item .totalStyle {
        text-align: center;
    }

    .title .numStyle,
    .item .numStyle {
        text-align: center;
    }

    .title .amountStyle,
    .item .amountStyle {
        text-align: right;
    }
}

/* 响应式设计 - 在小屏幕上进一步优化 */
@media (max-width: 768px) {
    .warrper {
        .name {
            width: 45%;
        }

        .totalStyle {
            width: 20%;
        }

        .numStyle {
            width: 15%;
        }

        .amountStyle {
            width: 20%;
        }
    }
}
</style>
