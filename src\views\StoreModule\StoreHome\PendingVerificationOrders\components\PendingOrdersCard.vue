<template>
  <div class="wrapper">
    <!-- 单选框 -->
    <VanRadio
      v-if="showCheckboxRef"
      checked-color="#EF1115"
      v-model:checked="isCheckedRef"
      @click.stop="handleRadioClick"
    ></VanRadio>
    <div
      class="pending_orders_card_wrapper"
      @click="handleCardClick"
    >
      <div class="pending-orders-header">
        <div class="order-code">
          <span>{{ `订单编号：${orderInfoRef?.code}` }}</span>
          <img :src="CopySrc" alt="" @click.stop="handleCopyID(orderInfoRef?.code)" />
        </div>
        <!-- 状态 -->
        <span class="pending-orders-status" :style="{ fontSize: isStoreVerification ? '12px' : '14px' }">
          {{ orderStatusMap[orderInfoRef?.status] }}
        </span>
      </div>
      <!-- 订单信息 -->
      <div class="pending-orders-info">
        <img :src="firstOrderItem?.productImgPath ? firstOrderItem?.productImgPath : CouponSrc" alt="" />
        <!-- 是否积分兑换、福利券兑换 -->
        <div
          v-if="[StoreOrderTypeEnum.COUPON, StoreOrderTypeEnum.INTEGRAL].includes(orderInfoRef?.type)"
          class="pending-orders-type"
          :style="{ backgroundColor: orderInfoRef?.type === StoreOrderTypeEnum.COUPON ? '#4BE092' : '#FFBC47' }"
        >
          {{ orderTypeMap[orderInfoRef?.type] }}
        </div>
        <div class="pending-orders-info-text">
          <div class="pending-orders-info-title">
            <p class="van-multi-ellipsis--l2">{{ firstOrderItem?.productFrontName }}</p>
            <div class="pending-orders-info-right">
              <!-- 价格 -->
              <span v-if="[StoreOrderTypeEnum.INTEGRAL].includes(orderInfoRef?.type)" class="price">
                {{ firstOrderItem.exchangePoints || 0 }}{{ firstOrderItem.exchangePrice ? '积分+￥' + (firstOrderItem.exchangePrice / 100) : '积分' }}
              </span>
              <span v-else class="price">
                {{ `¥ ${Number((firstOrderItem?.price ?? 0) / 100).toFixed(2)}` }}
              </span>
              <!-- 数量 -->
              <span class="count">{{ `x ${firstOrderItem?.count}` }}</span>
            </div>
          </div>
          <!-- 规格 -->
          <div class="specification">{{ firstOrderItem?.specName }}</div>
          <!-- 订单金额 -->
          <div class="order-amount">{{ `订单金额￥${Number(orderInfoRef?.money / 100).toFixed(2)}` }}</div>
        </div>
      </div>
      <!-- footer -->
      <div v-if="!props.showCheckbox" class="footer">
        <!-- 核销码 -->
        <div v-if="isSelfPickUp" class="write-off-order" @click.stop="handleWriteOffOrder(orderInfoRef)">核销订单</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { toRefs, computed, ref, watch } from "vue";
import { showToast } from "vant";
import { copyText } from "@/utils/clipboardUtils";
import { StoreOrderTypeEnum, StoreOrderStatusEnum, ProductPickupModeEnum, OrderVerificationTypeEnum } from "@/views/StoreModule/enums";
/** 静态资源 */
import CopySrc from "@/assets/image/member/copy.png";
import CouponSrc from "@/assets/storeImage/storeHome/coupon.png";

defineOptions({ name: 'PendingOrdersCard' });

/** props */
const props = withDefaults(defineProps<{
  // 是否显示单选框
  showCheckbox?: boolean;
  // 是否默认选中
  checked?: boolean;
  // 是否可选中（用于控制某些订单不可选）
  selectable?: boolean;
  orderInfo: {
    type?: StoreOrderTypeEnum;
    status?: StoreOrderStatusEnum;
    code?: string;
    verifyCode?: string;
    pickupType?: number;
    money?: number;
    goodsAmount?: number;
    payStatus?: number;
    afterSaleState?: number;
    verificationType?: OrderVerificationTypeEnum;
    orderItemDTOList?: Array<{
      type?: 1 | 2 | 3;
      orderId?: string;
      productImgPath?: string;
      productFrontName?: string;
      specName?: string;
      price?: number;
      count?: number;
      exchangePoints?: number;
      exchangePrice?: number;
    }>;
    orderVerificationDTO?: {
      code?: string;
    };
  };
}>(), {
  showCheckbox: false,
  checked: false,
  selectable: true
});

/** emit */
const emit = defineEmits<{
  (e: 'writeOffOrder', orderInfo): void;
  (e: 'update:checked', checked: boolean): void;
  (e: 'change', checked: boolean, orderInfo): void;
}>();

const { orderInfo: orderInfoRef, showCheckbox: showCheckboxRef, checked: checkedProp, selectable: selectableRef } = toRefs(props);

/** 选中状态 */
const isCheckedRef = ref(checkedProp.value);

/** 监听父组件传递的checked值变化 */
watch(checkedProp, (newVal) => {
  isCheckedRef.value = newVal;
});

/** 核销订单 */
function handleWriteOffOrder(orderInfo) {
  emit('writeOffOrder', orderInfo);
}

/** 点击单选框 */
function handleRadioClick() {
  if (!selectableRef.value) return;

  const newVal = !isCheckedRef.value;
  isCheckedRef.value = newVal;
  
  // 通知父组件
  emit('update:checked', newVal);
  emit('change', newVal, orderInfoRef.value);
}

/** 点击卡片 */
function handleCardClick() {
  if (!showCheckboxRef.value || !selectableRef.value) return;

  const newVal = !isCheckedRef.value;
  isCheckedRef.value = newVal;
  
  // 通知父组件
  emit('update:checked', newVal);
  emit('change', newVal, orderInfoRef.value);
}

/**
 * @description 计算属性
 */

/** 商品是否下单门店到货后核销 */
const isStoreVerification = computed(() => {
  return orderInfoRef.value?.verificationType == OrderVerificationTypeEnum.STORE_VERIFICATION;
});

/** 商品是否快递到家 */
const isHomeDelivery = computed(() => {
  return orderInfoRef.value?.pickupType == ProductPickupModeEnum.HOME_DELIVERY;
});

/** 商品是否自提 */
const isSelfPickUp = computed(() => {
  return orderInfoRef.value?.pickupType == ProductPickupModeEnum.STORE_PICKUP;
});

/** 获取第一项订单项 */
const firstOrderItem = computed(() => {
  return orderInfoRef.value?.orderItemDTOList?.[0];
});

/** 待发货提示语 */
function getWaitSendStatus() {
  if (isHomeDelivery.value) return '待发货';
  if (isStoreVerification.value) return '待发货至门店后提货';
  return '待提货';
};

/** 订单类型 */
const orderTypeMap = {
  [StoreOrderTypeEnum.INTEGRAL]: '积分兑换',
  [StoreOrderTypeEnum.COUPON]: '福利券兑换',
};

/** 订单状态 */
const orderStatusMap = {
  [StoreOrderStatusEnum.WAIT_PAY]: '待付款',
  [StoreOrderStatusEnum.WAIT_SEND]: getWaitSendStatus(),
  [StoreOrderStatusEnum.WAIT_RECEIVE]: '待收货',
  [StoreOrderStatusEnum.FINISHED]: '已完成',
  [StoreOrderStatusEnum.CANCELLED]: '已取消',
};

/** 复制订单ID */
function handleCopyID(data) {
  try {
    copyText(data);
    showToast('复制订单号成功');
  }
  catch (e) {
    showToast('复制订单号失败');
  }
}

// 暴露方法给父组件
defineExpose({
  setChecked: (checked: boolean) => {
    isCheckedRef.value = checked;
  },
  getChecked: () => isCheckedRef.value,
  getOrderInfo: () => orderInfoRef.value
});
</script>

<style lang="less" scoped>
.wrapper {
  display: flex;
  align-items: flex-start;
  gap: 12px;

  .van-radio {
    margin-top: 12px;

    &:deep(.van-radio__icon) {
      font-size: 18px;
    }
  }

  .pending_orders_card_wrapper {
    flex: 1;
    width: 100%;
    background: #FFFFFF;
    border-radius: 8px;
    padding: 12px;
    box-sizing: border-box;
    margin-bottom: 8px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    transition: all 0.3s ease;
    cursor: pointer;

    .pending-orders-header {
      display: flex;
      align-items: center;
      gap: 4px;

      .order-code {
        display: flex;
        align-items: center;

        span {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 500;
          font-size: 12px;
          color: #333333;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }

        img {
          width: 16px;
          height: 16px;
          margin-left: 4px;
        }
      }

      .pending-orders-status {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #4DA4FF;
        text-align: right;
        font-style: normal;
        text-transform: none;
        margin-left: auto;
      }
    }

    .pending-orders-info {
      display: flex;
      gap: 8px;
      position: relative;

      img {
        width: 64px;
        height: 64px;
        border-radius: 8px;
      }

      .pending-orders-type {
        position: absolute;
        top: 0;
        left: 0;
        width: 53px;
        height: 18px;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 8px;
        color: #FFFFFF;
        text-align: left;
        font-style: normal;
        text-transform: none;
        border-top-left-radius: 8px;
        border-bottom-right-radius: 8px;
        display: flex;
        justify-content: center;
        align-items: center;
        box-sizing: border-box;
      }

      .pending-orders-info-text {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 8px;

        .pending-orders-info-title {
          display: flex;
          gap: 12px;

          p {
            flex: 1;
            font-family: Source Han Sans CN, Source Han Sans CN;
            font-weight: 500;
            font-size: 16px;
            color: #333333;
            text-align: left;
            font-style: normal;
            text-transform: none;
            line-height: 22px;
          }

          .pending-orders-info-right {
            display: flex;
            flex-direction: column;
            gap: 8px;

            .price {
              font-family: Source Han Sans CN, Source Han Sans CN;
              font-weight: 600;
              font-size: 15px;
              color: #333333;
              text-align: right;
              font-style: normal;
              text-transform: none;
            }

            .count {
              font-family: Source Han Sans CN, Source Han Sans CN;
              font-weight: 400;
              font-size: 13px;
              color: #333333;
              text-align: right;
              font-style: normal;
              text-transform: none;
            }
          }
        }

        .specification {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          font-size: 14px;
          color: #666666;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }

        .order-amount {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 500;
          font-size: 16px;
          color: #333333;
          line-height: 24px;
          text-align: right;
          font-style: normal;
          text-transform: none;
        }
      }
    }

    .footer {
      width: 100%;
      margin-top: 6px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      gap: 8px;

      .write-off-order {
        height: 100%;
        border-radius: 999px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }

      .write-off-order {
        width: 80px;
        min-height: 28px;
        background: #FFF4F4;
        color: #EF1115;
      }
    }
  }
}
</style>
