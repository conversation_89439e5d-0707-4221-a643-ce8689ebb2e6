<template>
  <JLoadingWrapper :show="isPageLoadingRef">
    <div class="store_sales_statistics">
      <!-- 通知 -->
      <div class="statistics-notice">
        <VanNoticeBar left-icon="volume-o" color="#1677FF" background="#ECF5FF" style="height: 36px;" scrollable>
          <p class="notice-title">按支付时间统计购物类订单数据，已排除发起退款的订单</p>
        </VanNoticeBar>
      </div>
      <!-- 筛选条件 -->
      <div class="screening_condition_wrapper">
        <div class="screening_condition_header">
          <TagSelectGroup :data="dataRangeList" v-model:value="model.dataRangeRef" />
        </div>
        <!-- 时间筛选 -->
        <JDateRangePicker v-model:value="model.timeRef" />
      </div>
      <!-- 订单信息 -->
      <div class="store_sales_statistics_info">
        <!-- 商品名 + 规格 -->
        <div v-if="productSpecName" class="title" style="flex: 3;">{{ productSpecName }}</div>
        <!-- 表头 -->
        <div class="store_sales_statistics_info_header">
          <div class="store_sales_statistics_info_header_item" style="flex: 1;margin-left: 8px;">商品总数</div>
          <div class="store_sales_statistics_info_header_item" style="flex: 1;">商品单价</div>
          <div class="store_sales_statistics_info_header_item" style="flex: 1;">商品活动价</div>
          <div class="store_sales_statistics_info_header_item" style="flex: 1;">订单数</div>
          <div class="store_sales_statistics_info_header_item" style="flex: 1;">订单总额</div>
        </div>
        <!-- 表体 -->
        <div class="store_sales_statistics_info_body">
          <VanPullRefresh
            v-model="refreshingRef"
            @refresh="onRefresh"
            class="store_sales_list_content"
            :class="{ 'store_sales_list_content_h1': !productSpecName, 'store_sales_list_content_h2': productSpecName }"
          >
            <template v-if="StoreSalesOrderDetailList.length">
              <VanList v-model:loading="loadingRef" :finished="isFinishedRef" finished-text="没有更多了" @load="onLoad">
                <StoreSalesOrderDetailCard
                  v-for="item, index in StoreSalesOrderDetailList"
                  :key="index"
                  :orderInfo="item"
                  class="store_sales_list_item"
                />
              </VanList>
            </template>
            <template v-else>
              <EmptyData />
            </template>
          </VanPullRefresh>
        </div>
      </div>
    </div>
  </JLoadingWrapper>
</template>

<script lang="ts" setup>
import { ref, watch, computed, onMounted, effectScope, onScopeDispose } from "vue";
import dayjs from "dayjs";
import { DataRangeValue } from "@/views/StoreModule/enums";
import { usePaginatedFetch } from "@/views/StoreModule/hooks";
import { pageProductSellStatDataDetail } from "@/services/storeApi";
/** 相关组件 */
import TagSelectGroup from "@/views/StoreModule/components/TagSelectGroup.vue";
import JDateRangePicker from "@/views/StoreModule/components/JDateRangePicker.vue";
import EmptyData from "@/views/StoreModule/components/EmptyData.vue";
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";
import StoreSalesOrderDetailCard from "./components/StoreSalesOrderDetailCard.vue";

defineOptions({ name: 'StoreSalesOrderDetail' });

/** props */
const props = defineProps<{
  /** 开始时间 */
  dateStart: string;
  /** 结束时间 */
  dateEnd: string;
  /** 店长/店员短ID */
  staffShortId: string;
  /** 会员短ID */
  csShortId: string;
  /** 商品ID */
  productId: string;
  /** 商品规格ID */
  specId: string;
}>();

const scope = effectScope();

/** 时间筛选 */
const dataRangeList = [
  {
    label: "今日",
    value: DataRangeValue.TODAY,
  },
  {
    label: "近七天",
    value: DataRangeValue.SEVEN_DAYS,
  },
  {
    label: "近30天",
    value: DataRangeValue.THIRTY_DAYS,
  },
  {
    label: "近90天",
    value: DataRangeValue.NINETY_DAYS,
  },
];

// 添加防抖标志
const isRequestPending = ref(false);

/** 筛选数据 */
const initParams = {
  dataRangeRef: DataRangeValue.TODAY,
  timeRef: [dayjs().startOf("day").format("YYYY-MM-DD"), dayjs().endOf("day").format("YYYY-MM-DD")] as [
    string,
    string,
  ],
  staffShortId: props.staffShortId ?? "",
  csShortId: props.csShortId ?? "",
  productId: props.productId ?? "",
  specId: props.specId ?? "",
};
const model = ref({ ...initParams });

/** 分页数据加载Hook */
const {
  isPageLoadingRef,
  setPageLoadingTrue,
  setPageLoadingFalse,
  refreshingRef,
  loadingRef,
  isFinishedRef,
  searchParamsRef,
  pageListRef: StoreSalesOrderDetailList,
  initPageDataRequest: initStoreSalesOrderDetail,
  onLoad,
  onRefresh,
} = usePaginatedFetch({
  fetchApi: pageProductSellStatDataDetail,
  searchParams: {},
  beforeRequest: (params) => {
    return new Promise((resolve) => {
      resolve({
        ...params,
        data: {
          ...params.data,
          ...getSearchParams(),
        }
      });
    });
  }
});

/** 商品名 + 规格 */
const productSpecName = computed(() => {
  if (StoreSalesOrderDetailList.value.length) {
    const { productName, specName } = StoreSalesOrderDetailList.value[0];
    return `${productName}(${specName})`;
  }
  return "";
})

/** 获取搜索参数 */
function getSearchParams() {
  const { timeRef, staffShortId, csShortId, productId, specId } = model.value;
  const [startTime, endTime] = timeRef;

  return {
    dateStart: dayjs(startTime).format("YYYY-MM-DD 00:00:00"),
    dateEnd: dayjs(endTime).format("YYYY-MM-DD 23:59:59"),
    staffShortId,
    csShortId,
    productId,
    specId
  };
}

/** 获取时间 */
function getDateRange(value: DataRangeValue): [string, string] {
  const today = dayjs().endOf("day");
  let startTime;

  switch (value) {
    case DataRangeValue.TODAY:
      startTime = today.startOf("day");
      break;
    case DataRangeValue.SEVEN_DAYS:
      startTime = today.subtract(6, "day").startOf("day");
      break;
    case DataRangeValue.THIRTY_DAYS:
      startTime = today.subtract(29, "day").startOf("day");
      break;
    case DataRangeValue.NINETY_DAYS:
      startTime = today.subtract(89, "day").startOf("day");
      break;
    default:
      startTime = today.startOf("day");
  }

  return [startTime.format("YYYY-MM-DD"), today.format("YYYY-MM-DD")];
}

/**
 * 判断时间区间属于哪个范围
 */
function classifyTimeRange(start: string, end: string): DataRangeValue | null {
  const presetRanges = [
    DataRangeValue.TODAY,
    DataRangeValue.SEVEN_DAYS,
    DataRangeValue.THIRTY_DAYS,
    DataRangeValue.NINETY_DAYS,
  ].map(value => ({
    value,
    range: getDateRange(value),
  }));

  let startTime = dayjs(start).format("YYYY-MM-DD");
  let endTime = dayjs(end).format("YYYY-MM-DD");

  for (const { value, range } of presetRanges) {
    const [presetStart, presetEnd] = range;
    if (startTime === presetStart && endTime === presetEnd) {
      console.log("classifyTimeRange", value);
      
      return value;
    }
  }
  return null;
}

/** 发起数据请求（防抖处理） */
async function triggerDataRequest() {
  if (isRequestPending.value) return;
  
  isRequestPending.value = true;
  setPageLoadingTrue();
  
  try {
    await initStoreSalesOrderDetail();
  } finally {
    setPageLoadingFalse();
    // 使用 setTimeout 确保请求完成后再重置标志
    setTimeout(() => {
      isRequestPending.value = false;
    }, 100);
  }
}

/** 在作用域内运行监听器 */
scope.run(() => {
  /** 监听时间范围选择变化 - 主要触发源 */
  watch(
    () => model.value.dataRangeRef,
    async (newVal) => {
      if (newVal) {
        console.log('时间范围选择变化:', newVal);
        // 更新日期范围
        model.value.timeRef = getDateRange(newVal);
        // 这里不立即请求，由时间范围变化监听器统一处理
      }
    },
    {
      immediate: true,
    },
  );

  /** 监听时间范围变化 - 统一处理请求 */
  watch(
    () => model.value.timeRef,
    async (newVal, oldVal) => {
      if (newVal && newVal.length === 2) {
        console.log('时间范围变化:', newVal, '旧值:', oldVal);
        
        // 验证时间有效性
        if (!dayjs(newVal[0]).isValid() || !dayjs(newVal[1]).isValid()) {
          console.error('无效的时间范围');
          return;
        }
        
        // 尝试匹配预设值（仅用于UI显示）
        const matchedPreset = classifyTimeRange(newVal[0], newVal[1]);
        if (matchedPreset !== null) {
          model.value.dataRangeRef = matchedPreset;
        } else {
          model.value.dataRangeRef = null;
        }
        
        // 触发数据请求
        await triggerDataRequest();
      }
    },
    {
      deep: true
    }
  );

  watch(() => [props.dateStart, props.dateEnd], (newVal) => {
    if (newVal) {
      model.value.timeRef = [props.dateStart, props.dateEnd];
    }
  }, { immediate: true });
});

/** 作用域销毁时清理 */
onScopeDispose(() => {
  scope.stop();
});

/** 组件挂载 */
onMounted(async () => {
  await triggerDataRequest();
});
</script>

<style lang="less" scoped>
.store_sales_statistics {
  width: 100%;
  height: 100%;
  background: #F8F8F8;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;

  .statistics-notice {
    height: 36px;
    flex-shrink: 0;

    .notice-title {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #1677FF;
      line-height: 22px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }

  .screening_condition_wrapper {
    height: 96px;
    background: #FFFFFF;
    padding: 12px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex-shrink: 0;

    .screening_condition_header {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .screening_condition_header_right {
        display: flex;
        align-items: center;

        span {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          font-size: 14px;
          color: #666666;
          line-height: 20px;
          text-align: right;
          font-style: normal;
          text-transform: none;
        }

        .icon {
          transition: all 0.3s;
        }
      }
    }
  }

  .store_sales_statistics_info {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #FFFFFF;
    margin-top: 8px;
    box-sizing: border-box;

    .title {
      height: 32px;
      padding: 12px 12px 00px 12px;
      box-sizing: border-box;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      line-height: 20px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }

    .store_sales_statistics_info_header {
      height: 60px;
      display: flex;
      align-items: center;
      border-bottom: 1px solid #EEEEEE;
      padding: 12px 0px 12px 0px;
      box-sizing: border-box;

      .store_sales_statistics_info_header_item {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 13px;
        color: #666666;
        line-height: 18px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }

    .store_sales_statistics_info_body {
      flex: 1;
      box-sizing: border-box;

      .store_sales_list_content {
        box-sizing: border-box;
        overflow-y: auto;
      }

      .store_sales_list_content_h1 {
        height: calc(100vh - 200px - env(safe-area-inset-bottom));
      }

      .store_sales_list_content_h2 {
        height: calc(100vh - 200px - 32px - env(safe-area-inset-bottom));
      }

      .store_sales_list_item:nth-child(2n) {
        background: #F6FAFF;
      }

      .store_sales_list_item:hover {
        background: #E5F0FF;
      }
    }
  }
}

.rotate-180 {
  transform: rotate(-180deg);
}
</style>