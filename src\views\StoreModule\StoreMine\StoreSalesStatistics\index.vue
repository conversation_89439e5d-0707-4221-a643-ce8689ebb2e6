<template>
  <JLoadingWrapper :show="isPageLoadingRef">
    <div class="store_sales_statistics">
      <!-- 通知 -->
      <div class="statistics-notice">
        <VanNoticeBar
          left-icon="volume-o"
          color="#1677FF"
          background="#ECF5FF"
          style="height: 36px;"
          scrollable
        >
          <p class="notice-title">按支付时间统计购物类订单数据，已排除发起退款的订单</p>
        </VanNoticeBar>
      </div>
      <!-- 筛选条件 -->
      <div class="screening_condition_wrapper">
        <div class="screening_condition_header">
          <TagSelectGroup :data="dataRangeList" v-model:value="model.dataRangeRef" />
          <div class="screening_condition_header_right" @click="onShowScreening">
            <span>筛选</span>
            <SvgIcon
              name="dropDown"
              style="font-size: 18px;"
              :class="{ 'rotate-180': isShowStatisticalScreening, 'icon': true }"
            />
          </div>
        </div>
        <!-- 时间筛选 -->
        <JDateRangePicker v-model:value="model.timeRef" />
      </div>
      <!-- 订单信息 -->
      <div class="store_sales_statistics_info">
        <!-- 表头 -->
        <div class="store_sales_statistics_info_header">
          <div class="store_sales_statistics_info_header_item" style="width: 120px; padding-left: 8px;">商品名</div>
          <div class="store_sales_statistics_info_header_item" style="flex: 1;">商品总数</div>
          <!-- <div class="store_sales_statistics_info_header_item" style="flex: 1;">商品单价</div> -->
          <div class="store_sales_statistics_info_header_item" style="flex: 1;">订单数</div>
          <div class="store_sales_statistics_info_header_item" style="flex: 1;">订单总额</div>
        </div>
        <!-- 表体 -->
        <div class="store_sales_statistics_info_body">
          <VanPullRefresh v-model="refreshingRef" @refresh="onRefresh" class="store_sales_list_content">
            <template v-if="productSellStatDataPageListRef.length">
              <VanList v-model:loading="loadingRef" :finished="isFinishedRef" finished-text="没有更多了" @load="onLoad">
                <StoreOrderStatisticsCard
                  v-for="item, index in productSellStatDataPageListRef"
                  :key="item.productId + index" 
                  :orderInfo="item"
                  class="store_sales_list_item"
                  @click="onJumpToSalesDetail(item)"
                />
              </VanList>
            </template>
            <template v-else>
              <EmptyData />
            </template>
          </VanPullRefresh>
        </div>
      </div>
      <!-- 订单统计筛选 -->
      <JStatisticalScreening
        v-model:show="isShowStatisticalScreening"
        :model="{
          staffShortId: model.staffShortId,
          memberShortId: model.csShortId,
          productName: model.productName,
        }"
        @confirm="onConfirm"
        :isOrderType="false"
      />
    </div>
  </JLoadingWrapper>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted, effectScope, onScopeDispose } from "vue";
import dayjs from "dayjs";
import { RankTimeRangeEnum, DataRangeValue } from "@/views/StoreModule/enums";
import { usePaginatedFetch } from "@/views/StoreModule/hooks";
import { useRouterUtils } from "@/views/StoreModule/hooks";
import { RoutesName } from "@/enums/routes";
import { getproductSellStatDataPage } from "@/services/storeApi";
/** 相关组件 */
import TagSelectGroup from "@/views/StoreModule/components/TagSelectGroup.vue";
import JDateRangePicker from "@/views/StoreModule/components/JDateRangePicker.vue";
import JStatisticalScreening, { type ModelType } from "@/views/StoreModule/components/JStatisticalScreening.vue";
import EmptyData from "@/views/StoreModule/components/EmptyData.vue";
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";
import StoreOrderStatisticsCard from "./components/StoreOrderStatisticsCard.vue";

defineOptions({ name: 'StoreSalesStatistics' });

/** props */
const props = defineProps<{
  storeId?: string; // 店铺id
  /** 归属员工id */
  staffShortId?: string;
  /** 会员id */
  memberId?: string;
  /** 筛选时间 */
  dataRange: RankTimeRangeEnum;
}>();

const scope = effectScope();
const { routerPushByRouteName } = useRouterUtils();

/** 时间筛选 */
const dataRangeList = [
  {
    label: "今日",
    value: DataRangeValue.TODAY,
  },
  {
    label: "近七天",
    value: DataRangeValue.SEVEN_DAYS,
  },
  {
    label: "近30天",
    value: DataRangeValue.THIRTY_DAYS,
  },
  {
    label: "近90天",
    value: DataRangeValue.NINETY_DAYS,
  },
];

/** 筛选数据 */
const initParams = {
  dataRangeRef: DataRangeValue.TODAY,
  timeRef: [dayjs().startOf("day").format("YYYY-MM-DD"), dayjs().endOf("day").format("YYYY-MM-DD")] as [
    string,
    string,
  ],
  staffShortId: props.staffShortId,
  csShortId: props.memberId,
  productName: "",
  storeId: props.storeId ? props.storeId : undefined,
};
const model = ref({ ...initParams });

/** 分页数据加载Hook */
const {
  isPageLoadingRef,
  setPageLoadingTrue,
  setPageLoadingFalse,
  refreshingRef,
  loadingRef,
  isFinishedRef,
  searchParamsRef,
  pageListRef: productSellStatDataPageListRef,
  initPageDataRequest: initProductSellStatDataPageListRequest,
  onLoad,
  onRefresh,
} = usePaginatedFetch({
  fetchApi: getproductSellStatDataPage,
  searchParams: {
    storeId: props.storeId ? props.storeId : undefined,
  },
  beforeRequest: (params) => {
    return new Promise((resolve) => {
      resolve({
        ...params,
        data: {
          ...params.data,
          ...getSearchParams(),
        }
      });
    });
  }
});

/** 获取搜索参数 */
function getSearchParams() {
  const { storeId, timeRef, productName, staffShortId, csShortId } = model.value; 
  const [startTime, endTime] = timeRef;

  return {
    dateStart: dayjs(startTime).format("YYYY-MM-DD 00:00:00"),
    dateEnd: dayjs(endTime).format("YYYY-MM-DD 23:59:59"),
    storeId, 
    productName, 
    staffShortId, 
    csShortId
  };
}

const isShowStatisticalScreening = ref(false);

function onShowScreening() {
  isShowStatisticalScreening.value = !isShowStatisticalScreening.value;
}

/** 筛选确认回调 */
function onConfirm(data: ModelType) {
  const { staffShortId, memberShortId, productName } = data;
  Object.assign(model.value, {
    productName, 
    staffShortId, 
    csShortId: memberShortId
  });

  initProductSellStatDataPageListRequest();
}

/** 跳转销售详情 */
function onJumpToSalesDetail(salesOrderInfo) {
  const { timeRef, staffShortId, csShortId } = model.value; 
  const [startTime, endTime] = timeRef;
  const { productId, specId } = salesOrderInfo;

  routerPushByRouteName(RoutesName.StoreSalesOrderDetail, {
    /** 开始时间 */
    dateStart: startTime,
    /** 结束时间 */ 
    dateEnd: endTime,
    staffShortId, 
    csShortId,
    productId, 
    specId  
  });
}

/** 获取时间 */
function getDateRange(value: DataRangeValue): [string, string] {
  const today = dayjs().endOf("day");
  let startTime;

  switch (value) {
    case DataRangeValue.TODAY:
      startTime = today.startOf("day");
      break;
    case DataRangeValue.SEVEN_DAYS:
      startTime = today.subtract(6, "day").startOf("day");
      break;
    case DataRangeValue.THIRTY_DAYS:
      startTime = today.subtract(29, "day").startOf("day");
      break;
    case DataRangeValue.NINETY_DAYS:
      startTime = today.subtract(89, "day").startOf("day");
      break;
    default:
      startTime = today.startOf("day");
  }

  return [startTime.format("YYYY-MM-DD"), today.format("YYYY-MM-DD")];
}

/**
 * 判断时间区间属于哪个范围
 */
function classifyTimeRange(start: string, end: string): DataRangeValue | null {
  const presetRanges = [
    DataRangeValue.TODAY,
    DataRangeValue.SEVEN_DAYS,
    DataRangeValue.THIRTY_DAYS,
    DataRangeValue.NINETY_DAYS,
  ].map(value => ({
    value,
    range: getDateRange(value),
  }));

  let startTime = dayjs(start).format("YYYY-MM-DD");
  let endTime = dayjs(end).format("YYYY-MM-DD");

  for (const { value, range } of presetRanges) {
    const [presetStart, presetEnd] = range;
    if (startTime === presetStart && endTime === presetEnd) {
      return value;
    }
  }
  return null;
}

/** 在作用域内运行监听器 */
scope.run(() => {
  /** 监听 */
  watch(
    () => model.value.dataRangeRef,
    newVal => {
      if (newVal) {
        model.value.timeRef = getDateRange(newVal);
      }
    },
    {
      immediate: true,
    },
  );

  /** 监听时间范围变化尝试匹配预设值 */
  watch(
    () => model.value.timeRef,
    async (newVal) => {
      if (newVal) {
        const [start, end] = newVal;
        const matchedPreset = classifyTimeRange(start, end);
        model.value.dataRangeRef = matchedPreset;

        setPageLoadingTrue();
        await initProductSellStatDataPageListRequest();
        setPageLoadingFalse();
      }
    },
  );

  /** 监听携带时间参数 */
  watch(() => props.dataRange, (newVal) => {
    if (newVal) {
      // 本月
      if (newVal === RankTimeRangeEnum.THIS_MONTH) {
        model.value.timeRef = [dayjs().startOf('month').format('YYYY-MM-DD'), dayjs().endOf('month').format('YYYY-MM-DD')];
      }
      // 上月
      if (newVal === RankTimeRangeEnum.LAST_MONTH) {
        model.value.timeRef = [dayjs().subtract(1, 'month').startOf('month').format('YYYY-MM-DD'), dayjs().subtract(1, 'month').endOf('month').format('YYYY-MM-DD')];
      }
    }
  }, { immediate: true });
});

/** 作用域销毁时清理 */
onScopeDispose(() => {
  scope.stop();
});

/** 组件挂载 */
onMounted(async () => {
  if (props.dataRange) {
    return;
  }
  setPageLoadingTrue();
  await initProductSellStatDataPageListRequest();
  setPageLoadingFalse();
});
</script>

<style lang="less" scoped>
.store_sales_statistics {
  width: 100%;
  height: 100%;
  background: #F8F8F8;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;

  .statistics-notice {
    height: 36px;
    flex-shrink: 0;

    .notice-title {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #1677FF;
      line-height: 22px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }

  .screening_condition_wrapper {
    height: 96px;
    background: #FFFFFF;
    padding: 12px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex-shrink: 0;

    .screening_condition_header {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .screening_condition_header_right {
        display: flex;
        align-items: center;

        span {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 400;
          font-size: 14px;
          color: #666666;
          line-height: 20px;
          text-align: right;
          font-style: normal;
          text-transform: none;
        }

        .icon {
          transition: all 0.3s;
        }
      }
    }
  }

  .store_sales_statistics_info {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #FFFFFF;
    margin-top: 8px;
    box-sizing: border-box;

    .store_sales_statistics_info_header {
      height: 60px;
      display: flex;
      align-items: center;
      border-bottom: 1px solid #EEEEEE;
      padding: 12px 0px 12px 0px;
      box-sizing: border-box;

      .store_sales_statistics_info_header_item {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 13px;
        color: #666666;
        line-height: 18px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }

    .store_sales_statistics_info_body {
      flex: 1;
      box-sizing: border-box;

      .store_sales_list_content {
        height: calc(100vh - 200px - env(safe-area-inset-bottom));
        box-sizing: border-box;
        overflow-y: auto;
      }

      .store_sales_list_item:nth-child(2n) {
        background: #F6FAFF;
      }

      .store_sales_list_item:hover {
        background: #FFF4F4;
      }
    }
  }
}

.rotate-180 {
  transform: rotate(-180deg);
}
</style>
