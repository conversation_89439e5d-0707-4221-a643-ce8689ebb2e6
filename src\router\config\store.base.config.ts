import { RoutesName } from "@/enums/routes";
export const storeBaseRoutesConfig = [
    RoutesName.Check,
    RoutesName.Exception403,
    RoutesName.Exception404,
    RoutesName.Exception9000,
    RoutesName.Exception9001,
    RoutesName.Exception9002,
    RoutesName.Exception9003,
    RoutesName.Exception9004,
    RoutesName.Exception9005,
    RoutesName.Exception9006,
    RoutesName.Exception9008,
    RoutesName.Exception9007,
    RoutesName.Exception9009,
    RoutesName.GetCode,
    {
        name: RoutesName.StoreIndex,
        children: [
            RoutesName.StoreLive,
            RoutesName.StoreLogin,
            RoutesName.StoreInviteMember,
            RoutesName.StoreHome,
            RoutesName.StoreCoupon,
            RoutesName.StoreCouponDetail,
            RoutesName.StoreIntegral,
            RoutesName.StoreAccountSetting,
            RoutesName.StorePendingVerificationOrders,
            RoutesName.StoreOrderStatistics,
            RoutesName.StoreCategory,
            RoutesName.StoreSearch,
            RoutesName.StoreDetail,
            RoutesName.StoreMyOrders,
            RoutesName.StoreShopOrders,
            RoutesName.StoreMine,
            RoutesName.StoreOrderDetail,
            RoutesName.StoreAfterSaleDetail,
            RoutesName.StoreIntegralMall,
            RoutesName.StoreWelfareVoucherStatistics,
            RoutesName.StoreWelfareVoucherStatisticsDetail,
            RoutesName.StoreVerificationStatistics,
            RoutesName.StoreSalesStatistics,
            RoutesName.StoreSalesOrderDetail,
            RoutesName.StoreShopAssistantStatistics,
            RoutesName.StoreRanking,
            RoutesName.StoreRankingShop,
            RoutesName.StoreIntegralMallCanExchange,
            RoutesName.StoreWelfareVoucherMall,
            RoutesName.StoreConfirmOrder,
            RoutesName.StoreCashier,
            RoutesName.StoreAccountSetting,
            RoutesName.MemberManagement,
            RoutesName.StoreDefault,
            RoutesName.ViewDuration,
            RoutesName.MyDealer,
            RoutesName.StoreManagement,
            RoutesName.StoreSignup,
            RoutesName.StoreAddress,
            RoutesName.StoreAddressAdd,
            RoutesName.StoreCashCoupon,
            RoutesName.StoreReturnOrder,
            RoutesName.StoreRefundAudit,
            RoutesName.StoreApplyRefund,
            RoutesName.StoreWatchTime,
            RoutesName.StoreFillLogistics,
            RoutesName.StoreLogistics,
            RoutesName.StoreFillLogisticsNo,
            RoutesName.StoreDataExport,
            RoutesName.StoreExportRecord,
            RoutesName.StoreFileDownload,
            RoutesName.StoreWallet,
            RoutesName.StoreWithdrawRecord,
            RoutesName.StoreWithdraw,
            RoutesName.StoreBankCard,
            RoutesName.StorePickUpLocation,
        ],
    },
];
