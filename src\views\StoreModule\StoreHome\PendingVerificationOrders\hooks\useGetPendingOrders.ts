import { ref, reactive } from "vue";
import { SearchTypeEnum } from "../type";
import { StorePendingOrderRouteTypeEnum } from "@/views/StoreModule/enums";
import { useMessages } from "@/hooks/useMessage";
import { isNUllString } from "@/utils/isUtils"
import { getVerifyOrderList } from "@/services/storeApi";

export default function useGetPendingOrders(_params: { userId: string, type: StorePendingOrderRouteTypeEnum }) {
  const isPageLoadingRef = ref(false);

  const { createMessageSuccess, createMessageError } = useMessages();
  /** 待核销订单数据 */
  const pendingVerificationOrders = ref([]);
  /** 是否加载完 */
  const isFinishedRef = ref(false);
  /** 刷新 */
  const refreshingRef = ref(false);
  /** 加载 */
  const isLoadingRef = ref(false);
  /** 搜索值 */
  const searchValueRef = ref("");
  /** 搜索类型 */
  const searchTypeRef = ref(_params.type == StorePendingOrderRouteTypeEnum.MY_WAIT_VERIFY ? SearchTypeEnum.CustomerName : SearchTypeEnum.ProductName);

  /** 分页 */
  const pageVO = reactive({
    size: 100,
    current: 1,
    total: 0,
  });

  /** 获取搜索参数 */
  function getSearchParams() {
    const searchValue = searchValueRef.value.trim();
    const searchType = searchTypeRef.value;

    // 基础参数结构
    const params = {
      data: {
        customerId: !isNUllString(_params?.userId) ?  _params?.userId : undefined,
        customerNickname: null,
        productName: null,
        orderCode: null,
      },
      pageVO: {
        current: pageVO.current,
        size: pageVO.size,
      },
    };

    // 只有当有搜索值时才添加搜索条件
    if (searchValue) {
      switch (searchType) {
        case SearchTypeEnum.CustomerName:
          params.data.customerNickname = searchValue;
          break;
        case SearchTypeEnum.ProductName:
          params.data.productName = searchValue;
          break;
        case SearchTypeEnum.OrderNo:
          params.data.orderCode = searchValue;
          break;
      }
    }

    return params;
  }

  /** 加载数据 */
  function onLoad() {
    if (pageVO.current * pageVO.size < pageVO.total) {
      isLoadingRef.value = true;
      pageVO.current++;
      getPendingOrders();
    }
  }

  /** 初始化 */
  function ininParams() {
    pageVO.current = 1;
    pageVO.total = 0;
    isFinishedRef.value = false;
  }

  /** 刷新 */
  function onRefresh() {
    ininParams();
    // 重新加载数据
    refreshingRef.value = true;
    getPendingOrders();
  }

  /**
   * 获取待核销订单
   */
  async function getPendingOrders() {
    const { current, size } = pageVO;

    try {
      isPageLoadingRef.value = current === 1;
      const _params = getSearchParams();
      const { records = [], total = 0 } = await getVerifyOrderList(_params);

      // 更新订单列表
      if (current === 1) {
        pendingVerificationOrders.value = records;
      } else if (records.length) {
        pendingVerificationOrders.value.push(...records);
      }

      // 更新分页状态
      const hasMore = current * size < total;
      Object.assign(pageVO, {
        current: current,
        total: Number(total),
      });
      isFinishedRef.value = !hasMore;
    } catch (error) {
      createMessageError("加载失败，请稍后重试");
      ininParams();
    } finally {
      isLoadingRef.value = false;
      refreshingRef.value = false;
      isPageLoadingRef.value = false;
    }
  }

  /** 数据初始化 */
  async function initPendingOrdersList() {
    isPageLoadingRef.value = true;
    ininParams();
    await getPendingOrders();
    isPageLoadingRef.value = false;
  }

  return {
    isPageLoadingRef,
    pendingVerificationOrders,
    isFinishedRef,
    refreshingRef,
    isLoadingRef,
    searchValueRef,
    searchTypeRef,
    onLoad,
    onRefresh,
    getPendingOrders,
    initPendingOrdersList
  };
}
