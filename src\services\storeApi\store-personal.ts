import { defHttp } from "@/services";
import { getStoreApiUrl } from "@/utils/http/urlUtils";

/** 门店管理 */
export const enum storePersonalApiEnum {
  // 门店管理-经销商管理-店铺订单概览数据合计
  storeOrderOverview = "/h5/store/personal/countStoreSellStatData",
  // 门店管理-经销商管理-店铺订单概览数据统计-分页查询
  storeOrderOverviewPage = "/h5/store/personal/storeSellStatDataByDealer",
  // 门店管理-店铺待审核列表
  storePendingList = "/h5/store/personal/pageStoreAudit",
  // 审核门店
  auditStore = "/h5/store/personal/audit",
}

/**
 * @description 审核门店
 */
export function auditStoreApi(_params) {
  return defHttp.put({
    url: getStoreApiUrl(storePersonalApiEnum.auditStore),
    params: {
      data: _params,
    },
    requestConfig: {
      skipCrypto: true,
    },
  });
}

/**
 * @description 门店管理-经销商管理-店铺订单概览数据合计
 */
export function storeOrderOverviewApi(_params) {
  return defHttp.post({
    url: getStoreApiUrl(storePersonalApiEnum.storeOrderOverview),
    params: {
      data: _params,
    },
    requestConfig: {
      skipCrypto: true,
    },
  });
}

/**
 * @description 门店管理-经销商管理-店铺订单概览数据统计-分页查询
 */
export function storeOrderOverviewPageApi(_params) {
  return defHttp.post({
    url: getStoreApiUrl(storePersonalApiEnum.storeOrderOverviewPage),
    params: _params,
    requestConfig: {
      skipCrypto: true,
    },
  });
}

/**
 * @description 门店管理-店铺待审核列表
 */
export function storePendingListApi(_params) {
  return defHttp.post({
    url: getStoreApiUrl(storePersonalApiEnum.storePendingList),
    params: _params,
    requestConfig: {
      skipCrypto: true,
    },
  });
}
